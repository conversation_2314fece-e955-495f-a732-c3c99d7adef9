<?php
// Test Image Access - Specific for Alyanabea
echo "<h1>🖼️ Image Access Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// Get base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$path = str_replace(basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['SCRIPT_NAME']);
$base_url = $protocol . '://' . $host . $path;

echo "<h2>1. Configuration Check</h2>";
echo "<span class='info'>Base URL: $base_url</span><br>";
echo "<span class='info'>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</span><br>";

// Check properties directory
$properties_dir = './assets/images/properties/';
echo "<h2>2. Directory Status</h2>";

if (is_dir($properties_dir)) {
    echo "<span class='success'>✅ Properties directory exists</span><br>";
    echo "<span class='info'>Path: " . realpath($properties_dir) . "</span><br>";
    echo "<span class='info'>Permissions: " . substr(sprintf('%o', fileperms($properties_dir)), -4) . "</span><br>";
    echo "<span class='info'>Writable: " . (is_writable($properties_dir) ? 'YES' : 'NO') . "</span><br>";
} else {
    echo "<span class='error'>❌ Properties directory does not exist</span><br>";
    exit;
}

// List all images
echo "<h2>3. Available Images</h2>";
$files = array_diff(scandir($properties_dir), array('.', '..', 'index.html'));

if (count($files) > 0) {
    echo "<span class='success'>Found " . count($files) . " image files:</span><br><br>";
    
    foreach ($files as $file) {
        $file_path = $properties_dir . $file;
        $file_url = $base_url . 'assets/images/properties/' . $file;
        $file_size = filesize($file_path);
        $file_type = mime_content_type($file_path);
        
        echo "<div style='border:1px solid #ccc; padding:15px; margin:10px 0; background:#f9f9f9;'>";
        echo "<strong>📁 File: $file</strong><br>";
        echo "Size: " . number_format($file_size) . " bytes<br>";
        echo "Type: $file_type<br>";
        echo "Readable: " . (is_readable($file_path) ? '✅ YES' : '❌ NO') . "<br>";
        echo "URL: <a href='$file_url' target='_blank'>$file_url</a><br><br>";
        
        // Test image display
        echo "<strong>Display Test:</strong><br>";
        echo "<img src='$file_url' style='max-width:200px; max-height:150px; border:2px solid #ddd; margin:5px;' ";
        echo "onload=\"this.nextSibling.innerHTML='<span class=success>✅ Image loads successfully</span>';\" ";
        echo "onerror=\"this.nextSibling.innerHTML='<span class=error>❌ Image failed to load</span>';\">";
        echo "<div style='margin-top:5px; font-weight:bold;'>Loading...</div>";
        echo "</div>";
    }
} else {
    echo "<span class='error'>❌ No image files found</span><br>";
}

// Test logo image
echo "<h2>4. Logo Image Test</h2>";
$logo_path = './assets/images/logo.png';
if (file_exists($logo_path)) {
    $logo_url = $base_url . 'assets/images/logo.png';
    echo "<span class='success'>✅ Logo file exists</span><br>";
    echo "URL: <a href='$logo_url' target='_blank'>$logo_url</a><br>";
    echo "<img src='$logo_url' style='max-width:100px; border:1px solid #ccc; margin:10px 0;'>";
} else {
    echo "<span class='error'>❌ Logo file not found</span><br>";
}

// Test .htaccess
echo "<h2>5. .htaccess Analysis</h2>";
if (file_exists('.htaccess')) {
    echo "<span class='success'>✅ .htaccess file exists</span><br>";
    $htaccess_content = file_get_contents('.htaccess');
    echo "<pre style='background:#f0f0f0; padding:10px; border:1px solid #ccc;'>";
    echo htmlspecialchars($htaccess_content);
    echo "</pre>";
    
    if (strpos($htaccess_content, 'assets') !== false) {
        echo "<span class='success'>✅ Assets directory is excluded from URL rewriting</span><br>";
    } else {
        echo "<span class='error'>❌ Assets directory might be affected by URL rewriting</span><br>";
    }
} else {
    echo "<span class='error'>❌ .htaccess file not found</span><br>";
}

// Test direct access to assets
echo "<h2>6. Direct Asset Access Test</h2>";
$test_urls = array(
    'CSS' => $base_url . 'assets/css/style.css',
    'JS' => $base_url . 'assets/js/main.js',
    'Images Dir' => $base_url . 'assets/images/',
    'Properties Dir' => $base_url . 'assets/images/properties/'
);

foreach ($test_urls as $type => $url) {
    echo "$type: <a href='$url' target='_blank'>$url</a><br>";
}

// Check if mod_rewrite is causing issues
echo "<h2>7. URL Rewrite Test</h2>";
echo "<script>";
echo "function testImageLoad(url, elementId) {";
echo "  var img = new Image();";
echo "  img.onload = function() {";
echo "    document.getElementById(elementId).innerHTML = '<span class=\"success\">✅ Image loads via JavaScript</span>';";
echo "  };";
echo "  img.onerror = function() {";
echo "    document.getElementById(elementId).innerHTML = '<span class=\"error\">❌ Image failed to load via JavaScript</span>';";
echo "  };";
echo "  img.src = url;";
echo "}";
echo "</script>";

if (count($files) > 0) {
    $test_file = array_values($files)[0];
    $test_url = $base_url . 'assets/images/properties/' . $test_file;
    echo "Testing: $test_url<br>";
    echo "<div id='js-test'>Testing...</div>";
    echo "<script>testImageLoad('$test_url', 'js-test');</script>";
}

// Recommendations
echo "<h2>8. Troubleshooting Recommendations</h2>";
echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffeaa7; border-radius:5px;'>";
echo "<strong>If images exist but don't display:</strong><br>";
echo "1. <strong>Check web server permissions:</strong> Ensure web server can read files<br>";
echo "2. <strong>Check .htaccess rules:</strong> Make sure assets aren't blocked<br>";
echo "3. <strong>Check MIME types:</strong> Ensure server recognizes image types<br>";
echo "4. <strong>Check browser console:</strong> Look for 404 or 403 errors<br>";
echo "5. <strong>Test direct URL access:</strong> Try accessing image URLs directly<br>";
echo "</div>";

echo "<h2>9. Quick Fixes</h2>";
echo "<div style='background:#d4edda; padding:15px; border:1px solid #c3e6cb; border-radius:5px;'>";
echo "<strong>Try these commands:</strong><br>";
echo "<code>chmod 755 assets/</code><br>";
echo "<code>chmod 755 assets/images/</code><br>";
echo "<code>chmod 755 assets/images/properties/</code><br>";
echo "<code>chmod 644 assets/images/properties/*</code><br>";
echo "</div>";

// Auto-refresh option
echo "<h2>10. Auto-Refresh</h2>";
echo "<button onclick='location.reload()' style='background:#007bff; color:white; padding:10px 20px; border:none; border-radius:5px;'>🔄 Refresh Test</button>";
echo "<button onclick='window.open(\"" . $base_url . "admin\", \"_blank\")' style='background:#28a745; color:white; padding:10px 20px; border:none; border-radius:5px; margin-left:10px;'>🔗 Open Admin</button>";
?>
