<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Advertisements extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Property_model');
        $this->load->library('pagination');
    }

    public function index()
    {
        // Pagination configuration
        $config['base_url'] = $this->get_language_url('advertisements');
        $config['total_rows'] = $this->Property_model->count_all_properties();
        $config['per_page'] = 12;
        $config['uri_segment'] = ($this->current_language === 'english') ? 3 : 2;
        
        // Pagination styling for RTL/LTR
        $config['full_tag_open'] = '<nav><ul class="pagination justify-content-center">';
        $config['full_tag_close'] = '</ul></nav>';
        $config['first_link'] = ($this->current_language === 'arabic') ? 'الأول' : 'First';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_link'] = ($this->current_language === 'arabic') ? 'الأخير' : 'Last';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $config['next_link'] = ($this->current_language === 'arabic') ? 'التالي' : 'Next';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['prev_link'] = ($this->current_language === 'arabic') ? 'السابق' : 'Previous';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['attributes'] = array('class' => 'page-link');
        
        $this->pagination->initialize($config);
        
        $page = ($this->uri->segment($config['uri_segment'])) ? $this->uri->segment($config['uri_segment']) : 0;
        
        // Get filters
        $filters = array();
        if ($this->input->get('type')) {
            $filters['type'] = $this->input->get('type');
        }
        if ($this->input->get('min_price')) {
            $filters['min_price'] = $this->input->get('min_price');
        }
        if ($this->input->get('max_price')) {
            $filters['max_price'] = $this->input->get('max_price');
        }
        if ($this->input->get('location')) {
            $filters['location'] = $this->input->get('location');
        }
        
        $data['properties'] = $this->Property_model->get_properties($config['per_page'], $page, $filters);
        $data['pagination'] = $this->pagination->create_links();
        $data['property_types'] = $this->Property_model->get_property_types();
        $data['locations'] = $this->Property_model->get_locations();
        $data['filters'] = $filters;
        
        // Page meta data
        $data['page_title'] = $this->lang->line('nav_advertisements') . ' - ' . $this->lang->line('site_title');
        $data['meta_description'] = $this->lang->line('meta_description_properties');
        $data['page_class'] = 'properties-page';
        
        // Load views
        $this->load_view('templates/header', $data);
        $this->load_view('advertisements/index', $data);
        $this->load_view('templates/footer', $data);
    }

    public function view($id)
    {
        $property = $this->Property_model->get_property($id);
        
        if (!$property) {
            show_404();
        }
        
        // Get property title in current language
        $property_title = ($this->current_language === 'arabic') ? $property->title_ar : $property->title_en;
        
        $data['property'] = $property;
        $data['related_properties'] = $this->Property_model->get_related_properties($property->id, $property->type, 4);
        
        // Page meta data
        $data['page_title'] = $property_title . ' - ' . $this->lang->line('site_title');
        $data['meta_description'] = character_limiter(strip_tags(($this->current_language === 'arabic') ? $property->description_ar : $property->description_en), 160);
        $data['page_class'] = 'property-detail-page';
        //print_r($data); 
        // Load views
        $this->load_view('templates/header', $data);
        $this->load_view('advertisements/view', $data);
        $this->load_view('templates/footer', $data);
    }
}
