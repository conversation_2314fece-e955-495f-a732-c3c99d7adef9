<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Staff_model extends CI_Model {

    public function __construct()
    {
        parent::__construct();
    }

    public function get_all_staff()
    {
        $this->db->where('status', 'active');
        $this->db->order_by('display_order', 'ASC');
        $this->db->order_by('created_at', 'ASC');
        return $this->db->get('staff')->result();
    }

    public function get_staff_member($id)
    {
        $this->db->where('id', $id);
        $this->db->where('status', 'active');
        return $this->db->get('staff')->row();
    }

    public function get_featured_staff($limit = 4)
    {
        $this->db->where('featured', 1);
        $this->db->where('status', 'active');
        $this->db->order_by('display_order', 'ASC');
        $this->db->limit($limit);
        return $this->db->get('staff')->result();
    }

    public function count_staff()
    {
        $this->db->where('status', 'active');
        return $this->db->count_all_results('staff');
    }

    public function search_staff($keyword)
    {
        $this->db->select('*');
        $this->db->from('staff');
        $this->db->where('status', 'active');
        $this->db->group_start();
        $this->db->like('name_ar', $keyword);
        $this->db->or_like('name_en', $keyword);
        $this->db->or_like('position_ar', $keyword);
        $this->db->or_like('position_en', $keyword);
        $this->db->group_end();
        $this->db->order_by('display_order', 'ASC');
        
        return $this->db->get()->result();
    }
}
