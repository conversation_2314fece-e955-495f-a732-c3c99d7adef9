<?php
// Find Correct Path Script
echo "<h1>🔍 Find Correct Image Path</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

echo "<h2>1. Current Environment</h2>";
echo "Current working directory: <strong>" . getcwd() . "</strong><br>";
echo "Document root: <strong>" . $_SERVER['DOCUMENT_ROOT'] . "</strong><br>";
echo "Script filename: <strong>" . $_SERVER['SCRIPT_FILENAME'] . "</strong><br>";
echo "Script directory: <strong>" . dirname($_SERVER['SCRIPT_FILENAME']) . "</strong><br>";

echo "<h2>2. Testing Different Path Combinations</h2>";

// Test various path combinations to find where alyanabea_feb24 images are
$path_tests = [
    'Current directory' => './assets/images/properties/',
    'Relative assets' => 'assets/images/properties/',
    'Parent then alyanabea_feb24' => '../alyanabea_feb24/assets/images/properties/',
    'Document root + alyanabea_feb24' => $_SERVER['DOCUMENT_ROOT'] . '/alyanabea_feb24/assets/images/properties/',
    'Document root + assets' => $_SERVER['DOCUMENT_ROOT'] . '/assets/images/properties/',
    'Absolute alyanabea_feb24' => '/alyanabea_feb24/assets/images/properties/',
    'From script dir' => dirname($_SERVER['SCRIPT_FILENAME']) . '/assets/images/properties/',
    'From script dir + alyanabea_feb24' => dirname($_SERVER['SCRIPT_FILENAME']) . '/alyanabea_feb24/assets/images/properties/',
];

$working_paths = [];

foreach ($path_tests as $label => $path) {
    echo "<div style='border:1px solid #ccc; padding:10px; margin:10px 0;'>";
    echo "<strong>$label:</strong><br>";
    echo "&nbsp;&nbsp;Path: <code>$path</code><br>";
    
    $exists = is_dir($path);
    echo "&nbsp;&nbsp;Directory exists: " . ($exists ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "<br>";
    
    if ($exists) {
        $readable = is_readable($path);
        echo "&nbsp;&nbsp;Readable: " . ($readable ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "<br>";
        
        $files = array_diff(scandir($path), array('.', '..', 'index.html'));
        $file_count = count($files);
        echo "&nbsp;&nbsp;Files found: <span class='info'>$file_count</span><br>";
        
        if ($file_count > 0) {
            echo "&nbsp;&nbsp;Sample files: ";
            $sample_files = array_slice($files, 0, 3);
            foreach ($sample_files as $file) {
                echo "<code>$file</code> ";
            }
            echo "<br>";
            
            // This path works!
            $working_paths[] = [
                'label' => $label,
                'path' => $path,
                'file_count' => $file_count,
                'sample_files' => $sample_files
            ];
        }
    }
    echo "</div>";
}

echo "<h2>3. Working Paths Summary</h2>";

if (!empty($working_paths)) {
    echo "<div style='background:#d4edda; padding:15px; border:1px solid #c3e6cb; border-radius:5px;'>";
    echo "<strong>✅ Found " . count($working_paths) . " working path(s):</strong><br><br>";
    
    foreach ($working_paths as $working_path) {
        echo "<strong>" . $working_path['label'] . ":</strong><br>";
        echo "&nbsp;&nbsp;Path: <code>" . $working_path['path'] . "</code><br>";
        echo "&nbsp;&nbsp;Files: " . $working_path['file_count'] . "<br>";
        echo "&nbsp;&nbsp;Samples: ";
        foreach ($working_path['sample_files'] as $file) {
            echo "<code>$file</code> ";
        }
        echo "<br><br>";
    }
    echo "</div>";
} else {
    echo "<div style='background:#f8d7da; padding:15px; border:1px solid #f5c6cb; border-radius:5px;'>";
    echo "<strong>❌ No working paths found!</strong><br>";
    echo "The images directory might not exist or might be in a different location.";
    echo "</div>";
}

echo "<h2>4. Test with Actual Property Images</h2>";

// Try to connect to database to get actual property data
try {
    if (file_exists('application/config/database.php')) {
        include 'application/config/database.php';
        
        if (isset($db['default'])) {
            $host = $db['default']['hostname'];
            $username = $db['default']['username'];
            $password = $db['default']['password'];
            $database = $db['default']['database'];
            
            $connection = new mysqli($host, $username, $password, $database);
            
            if ($connection->connect_error) {
                echo "<span class='error'>❌ Database connection failed</span><br>";
            } else {
                echo "<span class='success'>✅ Database connected</span><br>";
                
                // Get some property images
                $query = "SELECT id, title_en, image FROM properties WHERE image IS NOT NULL AND image != '' LIMIT 3";
                $result = $connection->query($query);
                
                if ($result && $result->num_rows > 0) {
                    echo "<h3>Testing with actual property images:</h3>";
                    
                    while ($row = $result->fetch_assoc()) {
                        echo "<div style='border:1px solid #007bff; padding:10px; margin:10px 0; background:#f8f9ff;'>";
                        echo "<strong>Property ID: " . $row['id'] . "</strong><br>";
                        echo "Title: " . $row['title_en'] . "<br>";
                        echo "Image filename: <code>" . $row['image'] . "</code><br><br>";
                        
                        // Test each working path with this actual image
                        foreach ($working_paths as $working_path) {
                            $full_image_path = $working_path['path'] . $row['image'];
                            $exists = file_exists($full_image_path);
                            echo "&nbsp;&nbsp;<strong>" . $working_path['label'] . ":</strong> ";
                            echo ($exists ? "<span class='success'>✅ FOUND</span>" : "<span class='error'>❌ NOT FOUND</span>");
                            
                            if ($exists) {
                                $file_size = filesize($full_image_path);
                                echo " (Size: " . number_format($file_size) . " bytes)";
                            }
                            echo "<br>";
                        }
                        echo "</div>";
                    }
                } else {
                    echo "<span class='error'>❌ No properties with images found</span><br>";
                }
                
                $connection->close();
            }
        }
    } else {
        echo "<span class='error'>❌ Database config not found</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Error: " . $e->getMessage() . "</span><br>";
}

echo "<h2>5. Recommended Solution</h2>";

if (!empty($working_paths)) {
    $best_path = $working_paths[0]; // Use the first working path
    
    echo "<div style='background:#e8f5e8; padding:15px; border-left:4px solid #4CAF50;'>";
    echo "<strong>🎯 Recommended path to use:</strong><br><br>";
    echo "<code>\$file_system_path = '" . $best_path['path'] . "';</code><br><br>";
    echo "<strong>Update your view files with this path:</strong><br>";
    echo "1. application/views/advertisements/view.php<br>";
    echo "2. application/views/advertisements/index.php<br>";
    echo "3. application/views/admin/edit_property.php<br><br>";
    echo "<strong>Replace the current \$file_system_path with the path above.</strong>";
    echo "</div>";
} else {
    echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffeaa7; border-radius:5px;'>";
    echo "<strong>⚠️ No working paths found.</strong><br>";
    echo "Please check:<br>";
    echo "1. Does the alyanabea_feb24/assets/images/properties/ directory exist?<br>";
    echo "2. Are there image files in that directory?<br>";
    echo "3. Does the web server have permission to read the directory?<br>";
    echo "</div>";
}

echo "<h2>6. Manual Directory Check</h2>";
echo "<p>You can also manually check these locations:</p>";
echo "<ul>";
echo "<li>Look for a folder named 'alyanabea_feb24' on your server</li>";
echo "<li>Check if it contains: assets/images/properties/</li>";
echo "<li>Verify there are .jpg/.png files in that directory</li>";
echo "<li>Note the exact path from your current script location</li>";
echo "</ul>";

?>

<script>
// Auto-refresh option
setTimeout(function() {
    if (confirm('Refresh to re-run path detection?')) {
        location.reload();
    }
}, 30000);
</script>
