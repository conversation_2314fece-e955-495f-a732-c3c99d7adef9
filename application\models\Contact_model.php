<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Contact_model extends CI_Model {

    public function __construct()
    {
        parent::__construct();
    }

    public function save_contact($data)
    {
        // Add IP address and user agent for better tracking
        $data['ip_address'] = $this->input->ip_address();
        $data['user_agent'] = $this->input->user_agent();

        // Log the attempt for debugging
        log_message('info', 'Attempting to save contact: ' . json_encode($data));

        $result = $this->db->insert('contacts', $data);

        if (!$result) {
            log_message('error', 'Contact save failed: ' . $this->db->last_query());
            log_message('error', 'Database error: ' . json_encode($this->db->error()));
        } else {
            log_message('info', 'Contact saved successfully with ID: ' . $this->db->insert_id());
        }

        return $result;
    }

    public function get_contacts($limit = 10, $offset = 0)
    {
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get('contacts')->result();
    }

    public function get_contact($id)
    {
        $this->db->where('id', $id);
        return $this->db->get('contacts')->row();
    }

    public function count_contacts()
    {
        return $this->db->count_all('contacts');
    }

    public function update_contact_status($id, $status)
    {
        $this->db->where('id', $id);
        return $this->db->update('contacts', array('status' => $status));
    }

    public function get_contacts_by_language($language, $limit = 10, $offset = 0)
    {
        $this->db->where('language', $language);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get('contacts')->result();
    }
}
