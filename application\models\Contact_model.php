<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Contact_model extends CI_Model {

    public function __construct()
    {
        parent::__construct();
    }

    public function save_contact($data)
    {
        return $this->db->insert('contacts', $data);
    }

    public function get_contacts($limit = 10, $offset = 0)
    {
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get('contacts')->result();
    }

    public function get_contact($id)
    {
        $this->db->where('id', $id);
        return $this->db->get('contacts')->row();
    }

    public function count_contacts()
    {
        return $this->db->count_all('contacts');
    }

    public function update_contact_status($id, $status)
    {
        $this->db->where('id', $id);
        return $this->db->update('contacts', array('status' => $status));
    }

    public function get_contacts_by_language($language, $limit = 10, $offset = 0)
    {
        $this->db->where('language', $language);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get('contacts')->result();
    }
}
