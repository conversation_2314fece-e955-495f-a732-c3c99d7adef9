<?php
// Check Directory Structure
echo "<h1>📁 Directory Structure Check</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

echo "<h2>Current Location Analysis</h2>";
echo "Script location: <strong>" . __FILE__ . "</strong><br>";
echo "Current directory: <strong>" . getcwd() . "</strong><br>";
echo "Document root: <strong>" . $_SERVER['DOCUMENT_ROOT'] . "</strong><br>";

echo "<h2>Looking for 'alyanabea_feb24' folder...</h2>";

// Function to search for alyanabea_feb24 directory
function findAlyanabea($startPath, $maxDepth = 3, $currentDepth = 0) {
    $found = [];
    
    if ($currentDepth >= $maxDepth) {
        return $found;
    }
    
    if (!is_dir($startPath) || !is_readable($startPath)) {
        return $found;
    }
    
    $items = scandir($startPath);
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $fullPath = $startPath . DIRECTORY_SEPARATOR . $item;
        
        if (is_dir($fullPath)) {
            if ($item === 'alyanabea_feb24') {
                $found[] = $fullPath;
            } else {
                // Recursively search subdirectories
                $subFound = findAlyanabea($fullPath, $maxDepth, $currentDepth + 1);
                $found = array_merge($found, $subFound);
            }
        }
    }
    
    return $found;
}

// Search from current directory
echo "<h3>Searching from current directory...</h3>";
$currentDirResults = findAlyanabea(getcwd());

if (!empty($currentDirResults)) {
    foreach ($currentDirResults as $path) {
        echo "<span class='success'>✅ Found: $path</span><br>";
        
        // Check if it has the assets structure
        $assetsPath = $path . '/assets/images/properties';
        if (is_dir($assetsPath)) {
            echo "&nbsp;&nbsp;<span class='success'>✅ Has assets/images/properties</span><br>";
            
            $files = array_diff(scandir($assetsPath), array('.', '..', 'index.html'));
            echo "&nbsp;&nbsp;Files in properties: " . count($files) . "<br>";
            
            if (count($files) > 0) {
                echo "&nbsp;&nbsp;Sample files: ";
                $samples = array_slice($files, 0, 3);
                foreach ($samples as $file) {
                    echo "<code>$file</code> ";
                }
                echo "<br>";
            }
        } else {
            echo "&nbsp;&nbsp;<span class='error'>❌ No assets/images/properties</span><br>";
        }
        echo "<br>";
    }
} else {
    echo "<span class='error'>❌ No 'alyanabea_feb24' folder found from current directory</span><br>";
}

// Search from document root
echo "<h3>Searching from document root...</h3>";
$docRootResults = findAlyanabea($_SERVER['DOCUMENT_ROOT']);

if (!empty($docRootResults)) {
    foreach ($docRootResults as $path) {
        echo "<span class='success'>✅ Found: $path</span><br>";
        
        $assetsPath = $path . '/assets/images/properties';
        if (is_dir($assetsPath)) {
            echo "&nbsp;&nbsp;<span class='success'>✅ Has assets/images/properties</span><br>";
            
            $files = array_diff(scandir($assetsPath), array('.', '..', 'index.html'));
            echo "&nbsp;&nbsp;Files in properties: " . count($files) . "<br>";
        } else {
            echo "&nbsp;&nbsp;<span class='error'>❌ No assets/images/properties</span><br>";
        }
        echo "<br>";
    }
} else {
    echo "<span class='error'>❌ No 'alyanabea_feb24' folder found from document root</span><br>";
}

// Search parent directories
echo "<h3>Searching parent directories...</h3>";
$parentDir = dirname(getcwd());
$parentResults = findAlyanabea($parentDir);

if (!empty($parentResults)) {
    foreach ($parentResults as $path) {
        echo "<span class='success'>✅ Found: $path</span><br>";
        
        $assetsPath = $path . '/assets/images/properties';
        if (is_dir($assetsPath)) {
            echo "&nbsp;&nbsp;<span class='success'>✅ Has assets/images/properties</span><br>";
            
            $files = array_diff(scandir($assetsPath), array('.', '..', 'index.html'));
            echo "&nbsp;&nbsp;Files in properties: " . count($files) . "<br>";
        }
        echo "<br>";
    }
} else {
    echo "<span class='error'>❌ No 'alyanabea_feb24' folder found in parent directories</span><br>";
}

// Combine all results
$allResults = array_merge($currentDirResults, $docRootResults, $parentResults);
$allResults = array_unique($allResults);

echo "<h2>Summary & Recommendations</h2>";

if (!empty($allResults)) {
    echo "<div style='background:#d4edda; padding:15px; border:1px solid #c3e6cb; border-radius:5px;'>";
    echo "<strong>✅ Found alyanabea_feb24 folder(s):</strong><br><br>";
    
    foreach ($allResults as $path) {
        echo "<strong>Location:</strong> <code>$path</code><br>";
        
        // Calculate relative path from current script
        $currentScript = dirname(__FILE__);
        $relativePath = str_replace($currentScript, '.', $path);
        
        echo "<strong>Relative path from script:</strong> <code>$relativePath/assets/images/properties/</code><br>";
        
        // Check if this path has images
        $assetsPath = $path . '/assets/images/properties';
        if (is_dir($assetsPath)) {
            $files = array_diff(scandir($assetsPath), array('.', '..', 'index.html'));
            if (count($files) > 0) {
                echo "<span class='success'>✅ This path has " . count($files) . " image files</span><br>";
                echo "<strong>🎯 Use this path in your views:</strong><br>";
                echo "<code>\$file_system_path = '$relativePath/assets/images/properties/';</code><br>";
            }
        }
        echo "<br>";
    }
    echo "</div>";
} else {
    echo "<div style='background:#f8d7da; padding:15px; border:1px solid #f5c6cb; border-radius:5px;'>";
    echo "<strong>❌ Could not find 'alyanabea_feb24' folder</strong><br>";
    echo "Please check:<br>";
    echo "1. Is the folder named exactly 'alyanabea_feb24'?<br>";
    echo "2. Is it accessible from your current location?<br>";
    echo "3. Does it contain the assets/images/properties structure?<br>";
    echo "</div>";
}

echo "<h2>Manual Check Instructions</h2>";
echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffeaa7; border-radius:5px;'>";
echo "<strong>If automatic detection failed, manually check:</strong><br>";
echo "1. Open your file manager/FTP client<br>";
echo "2. Navigate to your web server directory<br>";
echo "3. Look for a folder named 'alyanabea_feb24'<br>";
echo "4. Check if it contains: assets/images/properties/<br>";
echo "5. Note the exact path relative to your current website<br>";
echo "6. Update the \$file_system_path variable accordingly<br>";
echo "</div>";

?>

<p><strong>Next step:</strong> Run this script and use the recommended path in your view files.</p>
