<?php
// Image Diagnostic Script for Alyanabea Admin
echo "<h1>Alyanabea Image System Diagnostic</h1>";

// Include CodeIgniter to access base_url and other functions
define('BASEPATH', TRUE);
require_once 'application/config/config.php';

// Get base URL from config
$base_url = isset($config['base_url']) ? $config['base_url'] : 'http://localhost/alyanabea-website/';

echo "<h2>1. Configuration Check</h2>";
echo "Base URL: " . $base_url . "<br>";
echo "Current URL: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

echo "<h2>2. Directory Structure Check</h2>";
$upload_dir = './assets/images/properties/';
echo "Upload directory: " . $upload_dir . "<br>";
echo "Directory exists: " . (is_dir($upload_dir) ? "✅ YES" : "❌ NO") . "<br>";
echo "Directory writable: " . (is_writable($upload_dir) ? "✅ YES" : "❌ NO") . "<br>";

if (is_dir($upload_dir)) {
    echo "Directory permissions: " . substr(sprintf('%o', fileperms($upload_dir)), -4) . "<br>";
}

echo "<h2>3. Existing Images Check</h2>";
if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    $image_files = array();
    
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && $file != 'index.html') {
            $file_path = $upload_dir . $file;
            $file_size = filesize($file_path);
            $file_type = mime_content_type($file_path);
            
            echo "📁 " . $file . "<br>";
            echo "&nbsp;&nbsp;&nbsp;Size: " . number_format($file_size) . " bytes<br>";
            echo "&nbsp;&nbsp;&nbsp;Type: " . $file_type . "<br>";
            echo "&nbsp;&nbsp;&nbsp;Readable: " . (is_readable($file_path) ? "✅" : "❌") . "<br>";
            
            // Test if image can be accessed via web
            $web_url = str_replace('./', $base_url, $file_path);
            echo "&nbsp;&nbsp;&nbsp;Web URL: <a href='" . $web_url . "' target='_blank'>" . $web_url . "</a><br>";
            
            $image_files[] = array(
                'file' => $file,
                'path' => $file_path,
                'url' => $web_url,
                'size' => $file_size,
                'type' => $file_type
            );
            
            echo "<br>";
        }
    }
} else {
    echo "❌ Directory does not exist!<br>";
}

echo "<h2>4. Image Display Test</h2>";
if (!empty($image_files)) {
    echo "Testing image display for existing files:<br><br>";
    
    foreach ($image_files as $img) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<strong>File: " . $img['file'] . "</strong><br>";
        echo "Size: " . number_format($img['size']) . " bytes | Type: " . $img['type'] . "<br>";
        echo "URL: " . $img['url'] . "<br><br>";
        
        echo "<img src='" . $img['url'] . "' style='max-width: 200px; max-height: 150px; border: 1px solid #ddd;' ";
        echo "onerror=\"this.style.display='none'; this.nextSibling.style.display='block';\">";
        echo "<div style='display:none; color:red; font-weight:bold;'>❌ Image failed to load</div>";
        echo "</div>";
    }
} else {
    echo "No images found to test.<br>";
}

echo "<h2>5. Upload Test Form</h2>";
?>

<form action="image_diagnostic.php" method="post" enctype="multipart/form-data" style="border: 1px solid #ccc; padding: 20px; background: #f9f9f9;">
    <h3>Test Image Upload</h3>
    <p>Select an image to test the upload functionality:</p>
    <input type="file" name="test_upload" accept="image/*" required>
    <br><br>
    <input type="submit" value="Upload Test Image" name="upload_test" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
</form>

<?php
// Handle upload test
if (isset($_POST['upload_test'])) {
    echo "<h2>6. Upload Test Results</h2>";
    
    if (isset($_FILES['test_upload']) && $_FILES['test_upload']['error'] == 0) {
        $upload_file = $_FILES['test_upload'];
        
        echo "<strong>Upload Details:</strong><br>";
        echo "Original name: " . $upload_file['name'] . "<br>";
        echo "Type: " . $upload_file['type'] . "<br>";
        echo "Size: " . number_format($upload_file['size']) . " bytes<br>";
        echo "Temp file: " . $upload_file['tmp_name'] . "<br>";
        
        // Generate encrypted filename like CodeIgniter does
        $file_ext = pathinfo($upload_file['name'], PATHINFO_EXTENSION);
        $encrypted_name = md5(uniqid(mt_rand(), true)) . '.' . $file_ext;
        $target_path = $upload_dir . $encrypted_name;
        
        echo "Target path: " . $target_path . "<br>";
        
        if (move_uploaded_file($upload_file['tmp_name'], $target_path)) {
            echo "<br>✅ <strong>Upload successful!</strong><br>";
            
            $web_url = str_replace('./', $base_url, $target_path);
            echo "Web URL: <a href='" . $web_url . "' target='_blank'>" . $web_url . "</a><br><br>";
            
            echo "Uploaded image preview:<br>";
            echo "<img src='" . $web_url . "' style='max-width: 300px; max-height: 200px; border: 2px solid #4CAF50;'>";
            
        } else {
            echo "<br>❌ <strong>Upload failed!</strong><br>";
            echo "Could not move file from temp location to target directory.<br>";
        }
        
    } else {
        echo "❌ Upload error occurred.<br>";
        if (isset($_FILES['test_upload'])) {
            echo "Error code: " . $_FILES['test_upload']['error'] . "<br>";
        }
    }
}

echo "<h2>7. CodeIgniter Integration Test</h2>";

// Test if we can connect to the database
try {
    // Simple database connection test
    $db_config = array();
    if (file_exists('application/config/database.php')) {
        include 'application/config/database.php';
        echo "✅ Database config file found<br>";
        
        if (isset($db['default'])) {
            echo "Database host: " . $db['default']['hostname'] . "<br>";
            echo "Database name: " . $db['default']['database'] . "<br>";
        }
    } else {
        echo "❌ Database config file not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "<br>";
}

echo "<h2>8. Recommendations</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50;'>";
echo "<strong>To fix image issues:</strong><br>";
echo "1. Ensure the assets/images/properties/ directory has write permissions (755 or 777)<br>";
echo "2. Check that your web server can serve files from the assets directory<br>";
echo "3. Verify the base_url in application/config/config.php is correct<br>";
echo "4. Test image upload using the form above<br>";
echo "5. Check browser console for any JavaScript errors<br>";
echo "6. Verify .htaccess file is not blocking access to image files<br>";
echo "</div>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }";
echo "h1, h2 { color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 5px; }";
echo "h3 { color: #4CAF50; }";
echo "</style>";
?>
