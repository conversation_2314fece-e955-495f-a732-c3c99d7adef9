-- <PERSON><PERSON> Al-Yanabea Real Estate Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS alyanabea_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE alyanabea_db;

-- Properties table with bilingual support
CREATE TABLE `properties` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title_ar` varchar(255) NOT NULL COMMENT 'Arabic title',
  `title_en` varchar(255) NOT NULL COMMENT 'English title',
  `description_ar` text NOT NULL COMMENT 'Arabic description',
  `description_en` text NOT NULL COMMENT 'English description',
  `type` enum('residential','commercial','administrative') NOT NULL,
  `type_ar` varchar(100) DEFAULT NULL COMMENT 'Arabic property type',
  `price` decimal(12,2) NOT NULL,
  `currency` varchar(10) DEFAULT 'SAR',
  `location_ar` varchar(255) NOT NULL COMMENT 'Arabic location',
  `location_en` varchar(255) NOT NULL COMMENT 'English location',
  `address_ar` text COMMENT 'Arabic address',
  `address_en` text COMMENT 'English address',
  `area` int(11) DEFAULT NULL COMMENT 'Area in square meters',
  `bedrooms` int(11) DEFAULT NULL,
  `bathrooms` int(11) DEFAULT NULL,
  `features_ar` text COMMENT 'Comma-separated features in Arabic',
  `features_en` text COMMENT 'Comma-separated features in English',
  `image` varchar(255) DEFAULT NULL COMMENT 'Main image',
  `gallery` text COMMENT 'JSON array of image paths',
  `featured` tinyint(1) DEFAULT 0,
  `status` enum('active','sold','rented','inactive') DEFAULT 'active',
  `views` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_price` (`price`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`featured`),
  KEY `idx_location_ar` (`location_ar`),
  KEY `idx_location_en` (`location_en`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contacts table with language support
CREATE TABLE `contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `language` varchar(10) DEFAULT 'arabic',
  `status` enum('new','read','replied') DEFAULT 'new',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_language` (`language`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Staff table with bilingual support
CREATE TABLE `staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name_ar` varchar(100) NOT NULL COMMENT 'Arabic name',
  `name_en` varchar(100) NOT NULL COMMENT 'English name',
  `position_ar` varchar(100) NOT NULL COMMENT 'Arabic position',
  `position_en` varchar(100) NOT NULL COMMENT 'English position',
  `bio_ar` text COMMENT 'Arabic biography',
  `bio_en` text COMMENT 'English biography',
  `image` varchar(255) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `experience_years` int(11) DEFAULT NULL,
  `specialties_ar` text COMMENT 'Comma-separated specialties in Arabic',
  `specialties_en` text COMMENT 'Comma-separated specialties in English',
  `featured` tinyint(1) DEFAULT 0,
  `display_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`featured`),
  KEY `idx_display_order` (`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Property inquiries table
CREATE TABLE `property_inquiries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `property_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `message` text,
  `inquiry_type` enum('viewing','purchase','rent','info') DEFAULT 'info',
  `language` varchar(10) DEFAULT 'arabic',
  `status` enum('new','contacted','scheduled','closed') DEFAULT 'new',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `property_id` (`property_id`),
  KEY `idx_status` (`status`),
  KEY `idx_language` (`language`),
  CONSTRAINT `property_inquiries_ibfk_1` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Property views tracking
CREATE TABLE `property_views` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `property_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `language` varchar(10) DEFAULT 'arabic',
  `viewed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `property_id` (`property_id`),
  KEY `idx_viewed_at` (`viewed_at`),
  CONSTRAINT `property_views_ibfk_1` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample staff data
INSERT INTO `staff` (`name_ar`, `name_en`, `position_ar`, `position_en`, `bio_ar`, `bio_en`, `experience_years`, `specialties_ar`, `specialties_en`, `featured`, `display_order`, `status`) VALUES
('أحمد محمد العلي', 'Ahmed Mohammed Al-Ali', 'مدير المكتب', 'Office Manager', 'مدير مكتب ألوان الينابيع للعقارات مع خبرة تزيد عن 15 عاماً في مجال العقارات وإدارة الأملاك', 'Office Manager at Alwan Al-Yanabea Real Estate with over 15 years of experience in real estate and property management', 15, 'إدارة الأملاك,التسويق العقاري,خدمة العملاء', 'Property Management,Real Estate Marketing,Customer Service', 1, 1, 'active'),
('فاطمة سعد الغامدي', 'Fatima Saad Al-Ghamdi', 'مستشارة عقارية', 'Real Estate Consultant', 'مستشارة عقارية متخصصة في العقارات السكنية والتجارية مع خبرة 8 سنوات', 'Real estate consultant specializing in residential and commercial properties with 8 years of experience', 8, 'العقارات السكنية,العقارات التجارية,الاستشارات العقارية', 'Residential Properties,Commercial Properties,Real Estate Consulting', 1, 2, 'active'),
('خالد عبدالله النمر', 'Khalid Abdullah Al-Nimer', 'مسؤول الصيانة', 'Maintenance Manager', 'مسؤول الصيانة والمتابعة الفنية للعقارات مع خبرة 12 عاماً في الصيانة والإشراف', 'Maintenance manager and technical follow-up for properties with 12 years of experience in maintenance and supervision', 12, 'الصيانة العامة,الإشراف الفني,إدارة المقاولين', 'General Maintenance,Technical Supervision,Contractor Management', 1, 3, 'active'),
('نورا إبراهيم الحربي', 'Nora Ibrahim Al-Harbi', 'منسقة خدمة العملاء', 'Customer Service Coordinator', 'منسقة خدمة العملاء المسؤولة عن متابعة العملاء والرد على استفساراتهم', 'Customer service coordinator responsible for following up with clients and responding to their inquiries', 5, 'خدمة العملاء,التنسيق,المتابعة', 'Customer Service,Coordination,Follow-up', 0, 4, 'active');

-- Insert sample properties data
INSERT INTO `properties` (`title_ar`, `title_en`, `description_ar`, `description_en`, `type`, `type_ar`, `price`, `location_ar`, `location_en`, `area`, `bedrooms`, `bathrooms`, `features_ar`, `features_en`, `featured`, `status`) VALUES
('شقة فاخرة في حي الملقا', 'Luxury Apartment in Al-Malqa District', 'شقة فاخرة مكونة من 3 غرف نوم وصالة ومطبخ مجهز بالكامل في حي الملقا الراقي', 'Luxury apartment consisting of 3 bedrooms, living room and fully equipped kitchen in the upscale Al-Malqa district', 'residential', 'سكني', 850000.00, 'حي الملقا، الرياض', 'Al-Malqa District, Riyadh', 180, 3, 2, 'مطبخ مجهز,موقف سيارة,مصعد,أمن 24 ساعة', 'Equipped Kitchen,Parking,Elevator,24h Security', 1, 'active'),
('فيلا راقية في حي النرجس', 'Upscale Villa in Al-Narjis District', 'فيلا راقية مكونة من دورين مع حديقة خاصة ومسبح في حي النرجس', 'Upscale two-story villa with private garden and swimming pool in Al-Narjis district', 'residential', 'سكني', 2500000.00, 'حي النرجس، الرياض', 'Al-Narjis District, Riyadh', 450, 5, 4, 'مسبح خاص,حديقة,موقف 4 سيارات,غرفة خادمة', 'Private Pool,Garden,4-Car Parking,Maid Room', 1, 'active'),
('مكتب تجاري في برج الفيصلية', 'Commercial Office in Al-Faisaliah Tower', 'مكتب تجاري مجهز بالكامل في برج الفيصلية مع إطلالة رائعة على المدينة', 'Fully equipped commercial office in Al-Faisaliah Tower with stunning city views', 'commercial', 'تجاري', 1200000.00, 'برج الفيصلية، الرياض', 'Al-Faisaliah Tower, Riyadh', 120, 0, 2, 'مجهز بالكامل,إطلالة رائعة,موقف سيارات,أمن', 'Fully Equipped,Great View,Parking,Security', 1, 'active'),
('شقة عائلية في حي الياسمين', 'Family Apartment in Al-Yasmin District', 'شقة عائلية مريحة مكونة من 4 غرف نوم في حي الياسمين الهادئ', 'Comfortable family apartment with 4 bedrooms in the quiet Al-Yasmin district', 'residential', 'سكني', 650000.00, 'حي الياسمين، الرياض', 'Al-Yasmin District, Riyadh', 200, 4, 3, 'شرفة واسعة,مطبخ حديث,موقف سيارتين', 'Large Balcony,Modern Kitchen,Two-Car Parking', 0, 'active'),
('مجمع إداري في حي العليا', 'Administrative Complex in Al-Olaya District', 'مجمع إداري متكامل في حي العليا التجاري مناسب للشركات والمؤسسات', 'Integrated administrative complex in Al-Olaya commercial district suitable for companies and institutions', 'administrative', 'إداري', 3500000.00, 'حي العليا، الرياض', 'Al-Olaya District, Riyadh', 800, 0, 8, 'قاعات اجتماعات,مواقف واسعة,أمن متطور,مصاعد', 'Meeting Halls,Large Parking,Advanced Security,Elevators', 1, 'active'),
('شقة استثمارية في حي الربوة', 'Investment Apartment in Al-Rabwa District', 'شقة استثمارية ممتازة في حي الربوة مع عائد استثماري مجزي', 'Excellent investment apartment in Al-Rabwa district with rewarding investment return', 'residential', 'سكني', 450000.00, 'حي الربوة، الرياض', 'Al-Rabwa District, Riyadh', 150, 2, 2, 'موقع استراتيجي,عائد مجزي,صيانة منخفضة', 'Strategic Location,Good Return,Low Maintenance', 0, 'active');

-- Insert sample contacts
INSERT INTO `contacts` (`name`, `email`, `phone`, `subject`, `message`, `language`) VALUES
('محمد أحمد السعيد', '<EMAIL>', '0501234567', 'استفسار عن شقة في الملقا', 'أرغب في الحصول على مزيد من المعلومات حول الشقة المعروضة في حي الملقا', 'arabic'),
('Sarah Johnson', '<EMAIL>', '0509876543', 'Inquiry about villa in Al-Narjis', 'I would like to get more information about the villa listed in Al-Narjis district', 'english'),
('عبدالله محمد الغامدي', '<EMAIL>', '0551234567', 'طلب زيارة للمكتب التجاري', 'أرغب في ترتيب موعد لزيارة المكتب التجاري في برج الفيصلية', 'arabic');

-- Create views for easier data access
CREATE VIEW active_properties_ar AS
SELECT 
    id, title_ar as title, description_ar as description, type, type_ar as type_display,
    price, location_ar as location, area, bedrooms, bathrooms, 
    features_ar as features, image, featured, views, created_at
FROM properties 
WHERE status = 'active';

CREATE VIEW active_properties_en AS
SELECT 
    id, title_en as title, description_en as description, type, type as type_display,
    price, location_en as location, area, bedrooms, bathrooms, 
    features_en as features, image, featured, views, created_at
FROM properties 
WHERE status = 'active';

CREATE VIEW featured_properties_ar AS
SELECT 
    id, title_ar as title, description_ar as description, type, type_ar as type_display,
    price, location_ar as location, area, bedrooms, bathrooms, 
    features_ar as features, image, views, created_at
FROM properties 
WHERE status = 'active' AND featured = 1;

CREATE VIEW featured_properties_en AS
SELECT 
    id, title_en as title, description_en as description, type, type as type_display,
    price, location_en as location, area, bedrooms, bathrooms, 
    features_en as features, image, views, created_at
FROM properties 
WHERE status = 'active' AND featured = 1;

-- Create indexes for better performance
CREATE INDEX idx_properties_title_ar ON properties(title_ar);
CREATE INDEX idx_properties_title_en ON properties(title_en);
CREATE INDEX idx_properties_price_range ON properties(price, type, status);
CREATE INDEX idx_contacts_date ON contacts(created_at DESC);
CREATE INDEX idx_staff_display ON staff(display_order, featured, status);

-- Create stored procedures for common operations
DELIMITER //

CREATE PROCEDURE GetPropertiesByLanguage(
    IN lang VARCHAR(10),
    IN property_type VARCHAR(50),
    IN min_price DECIMAL(12,2),
    IN max_price DECIMAL(12,2),
    IN limit_count INT,
    IN offset_count INT
)
BEGIN
    IF lang = 'english' THEN
        SELECT 
            id, title_en as title, description_en as description, type,
            price, location_en as location, area, bedrooms, bathrooms,
            features_en as features, image, featured, views, created_at
        FROM properties 
        WHERE status = 'active'
        AND (property_type IS NULL OR type = property_type)
        AND (min_price IS NULL OR price >= min_price)
        AND (max_price IS NULL OR price <= max_price)
        ORDER BY featured DESC, created_at DESC
        LIMIT limit_count OFFSET offset_count;
    ELSE
        SELECT 
            id, title_ar as title, description_ar as description, type, type_ar as type_display,
            price, location_ar as location, area, bedrooms, bathrooms,
            features_ar as features, image, featured, views, created_at
        FROM properties 
        WHERE status = 'active'
        AND (property_type IS NULL OR type = property_type)
        AND (min_price IS NULL OR price >= min_price)
        AND (max_price IS NULL OR price <= max_price)
        ORDER BY featured DESC, created_at DESC
        LIMIT limit_count OFFSET offset_count;
    END IF;
END //

DELIMITER ;
