<?php
// Debug Contact Form - Step by Step Analysis
echo "<h1>🔍 Contact Form Debug Analysis</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;}</style>";

echo "<h2>1. Form Submission Test</h2>";

if ($_POST) {
    echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffeaa7; border-radius:5px; margin:10px 0;'>";
    echo "<strong>📝 Form Data Received:</strong><br>";
    foreach ($_POST as $key => $value) {
        echo "<strong>$key:</strong> " . htmlspecialchars($value) . "<br>";
    }
    echo "</div>";
    
    // Test database connection
    if (file_exists('application/config/database.php')) {
        include 'application/config/database.php';
        
        if (isset($db['default'])) {
            $host = $db['default']['hostname'];
            $username = $db['default']['username'];
            $password = $db['default']['password'];
            $database = $db['default']['database'];
            
            try {
                $connection = new mysqli($host, $username, $password, $database);
                
                if ($connection->connect_error) {
                    echo "<span class='error'>❌ Database connection failed</span><br>";
                } else {
                    echo "<span class='success'>✅ Database connected</span><br>";
                    
                    // Check if contacts table exists
                    $table_check = $connection->query("SHOW TABLES LIKE 'contacts'");
                    
                    if ($table_check->num_rows > 0) {
                        echo "<span class='success'>✅ Contacts table exists</span><br>";
                        
                        // Try to insert the form data
                        $name = $connection->real_escape_string($_POST['name']);
                        $email = $connection->real_escape_string($_POST['email']);
                        $phone = $connection->real_escape_string($_POST['phone']);
                        $subject = $connection->real_escape_string($_POST['subject']);
                        $message = $connection->real_escape_string($_POST['message']);
                        
                        $sql = "INSERT INTO contacts (name, email, phone, subject, message, language, created_at) 
                                VALUES ('$name', '$email', '$phone', '$subject', '$message', 'en', NOW())";
                        
                        echo "<div style='background:#f8f9fa; padding:10px; border:1px solid #dee2e6; border-radius:5px; margin:10px 0;'>";
                        echo "<strong>SQL Query:</strong><br><code>$sql</code>";
                        echo "</div>";
                        
                        if ($connection->query($sql)) {
                            echo "<span class='success'>✅ Data inserted successfully! Insert ID: " . $connection->insert_id . "</span><br>";
                        } else {
                            echo "<span class='error'>❌ Insert failed: " . $connection->error . "</span><br>";
                        }
                        
                    } else {
                        echo "<span class='error'>❌ Contacts table does NOT exist</span><br>";
                    }
                }
                
                $connection->close();
                
            } catch (Exception $e) {
                echo "<span class='error'>❌ Database error: " . $e->getMessage() . "</span><br>";
            }
        }
    }
} else {
    echo "<span class='info'>ℹ️ No form data submitted yet. Use the form below to test.</span><br>";
}

echo "<h2>2. Test Contact Form</h2>";
?>

<form method="post" style="background:#f8f9fa; padding:20px; border:1px solid #dee2e6; border-radius:5px; max-width:600px;">
    <h3>Test Contact Form Submission</h3>
    
    <div style="margin-bottom:15px;">
        <label>Name:</label><br>
        <input type="text" name="name" value="Test User" required style="width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label>Email:</label><br>
        <input type="email" name="email" value="<EMAIL>" required style="width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label>Phone:</label><br>
        <input type="tel" name="phone" value="+966501234567" required style="width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label>Subject:</label><br>
        <input type="text" name="subject" value="Test Message" required style="width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;">
    </div>
    
    <div style="margin-bottom:15px;">
        <label>Message:</label><br>
        <textarea name="message" required style="width:100%; padding:8px; border:1px solid #ccc; border-radius:4px; height:100px;">This is a test message to debug the contact form.</textarea>
    </div>
    
    <input type="submit" value="Test Submit" style="background:#007bff; color:white; padding:10px 20px; border:none; border-radius:5px; cursor:pointer;">
</form>

<?php
echo "<h2>3. Database Status Check</h2>";

if (file_exists('application/config/database.php')) {
    include 'application/config/database.php';
    
    if (isset($db['default'])) {
        $host = $db['default']['hostname'];
        $username = $db['default']['username'];
        $password = $db['default']['password'];
        $database = $db['default']['database'];
        
        echo "Database: <strong>$database</strong><br>";
        echo "Host: <strong>$host</strong><br>";
        echo "Username: <strong>$username</strong><br>";
        
        try {
            $connection = new mysqli($host, $username, $password, $database);
            
            if ($connection->connect_error) {
                echo "<span class='error'>❌ Connection failed: " . $connection->connect_error . "</span><br>";
            } else {
                echo "<span class='success'>✅ Database connection successful</span><br>";
                
                // Check contacts table
                $table_check = $connection->query("SHOW TABLES LIKE 'contacts'");
                
                if ($table_check->num_rows > 0) {
                    echo "<span class='success'>✅ Contacts table exists</span><br>";
                    
                    // Show table structure
                    $structure = $connection->query("DESCRIBE contacts");
                    echo "<h4>Table Structure:</h4>";
                    echo "<table border='1' style='border-collapse:collapse;'>";
                    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
                    while ($row = $structure->fetch_assoc()) {
                        echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td></tr>";
                    }
                    echo "</table>";
                    
                    // Count records
                    $count_result = $connection->query("SELECT COUNT(*) as total FROM contacts");
                    $count = $count_result->fetch_assoc()['total'];
                    echo "<br><span class='info'>📊 Total records: <strong>$count</strong></span><br>";
                    
                } else {
                    echo "<span class='error'>❌ Contacts table does NOT exist</span><br>";
                    echo "<div style='background:#f8d7da; padding:15px; border:1px solid #f5c6cb; border-radius:5px; margin:10px 0;'>";
                    echo "<strong>🔧 Create the table with this SQL:</strong><br>";
                    echo "<textarea style='width:100%; height:200px; font-family:monospace; font-size:12px;'>";
                    echo "CREATE TABLE contacts (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(100) NOT NULL,
  email varchar(100) NOT NULL,
  phone varchar(20) NOT NULL,
  subject varchar(200) NOT NULL,
  message text NOT NULL,
  language varchar(2) DEFAULT 'ar',
  status enum('new','read','replied','archived') DEFAULT 'new',
  ip_address varchar(45) DEFAULT NULL,
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
                    echo "</textarea>";
                    echo "</div>";
                }
                
                $connection->close();
            }
            
        } catch (Exception $e) {
            echo "<span class='error'>❌ Error: " . $e->getMessage() . "</span><br>";
        }
    }
}

echo "<h2>4. CodeIgniter Contact Form Check</h2>";

// Check if CodeIgniter contact form is accessible
$contact_url = str_replace('debug_contact_form.php', 'اتصل-بنا', $_SERVER['REQUEST_URI']);
echo "Contact form URL: <a href='$contact_url' target='_blank'>$contact_url</a><br>";

// Check if contact controller exists
if (file_exists('application/controllers/Contact.php')) {
    echo "<span class='success'>✅ Contact controller exists</span><br>";
} else {
    echo "<span class='error'>❌ Contact controller missing</span><br>";
}

// Check if contact model exists
if (file_exists('application/models/Contact_model.php')) {
    echo "<span class='success'>✅ Contact model exists</span><br>";
} else {
    echo "<span class='error'>❌ Contact model missing</span><br>";
}

// Check if contact view exists
if (file_exists('application/views/contact/index.php')) {
    echo "<span class='success'>✅ Contact view exists</span><br>";
} else {
    echo "<span class='error'>❌ Contact view missing</span><br>";
}

echo "<h2>5. Next Steps</h2>";
echo "<div style='background:#e8f5e8; padding:15px; border-left:4px solid #4CAF50;'>";
echo "<strong>To fix the contact form:</strong><br>";
echo "1. <strong>Create contacts table</strong> if it doesn't exist (use SQL above)<br>";
echo "2. <strong>Test this form</strong> to verify database insertion works<br>";
echo "3. <strong>Check CodeIgniter logs</strong> in application/logs/ for errors<br>";
echo "4. <strong>Enable error reporting</strong> in index.php temporarily<br>";
echo "5. <strong>Test the actual contact form</strong> on your website<br>";
echo "</div>";

echo "<h2>6. Error Log Check</h2>";
$log_dir = 'application/logs/';
if (is_dir($log_dir)) {
    $log_files = glob($log_dir . '*.php');
    if (!empty($log_files)) {
        $latest_log = max($log_files);
        echo "Latest log file: <strong>" . basename($latest_log) . "</strong><br>";
        echo "Check this file for contact form errors.<br>";
    } else {
        echo "<span class='info'>ℹ️ No log files found</span><br>";
    }
} else {
    echo "<span class='warning'>⚠️ Logs directory not found</span><br>";
}
?>
