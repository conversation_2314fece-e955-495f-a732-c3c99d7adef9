<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| Email Configuration
|--------------------------------------------------------------------------
| Configuration for sending emails from the contact form
|
*/

// Email configuration for Alyanabea
$config['protocol'] = 'smtp';
$config['smtp_host'] = 'smtp.gmail.com'; // Change to your SMTP server
$config['smtp_port'] = 587;
$config['smtp_user'] = '<EMAIL>'; // Change to your email
$config['smtp_pass'] = 'your-app-password'; // Change to your app password
$config['smtp_crypto'] = 'tls';
$config['mailtype'] = 'html';
$config['charset'] = 'utf-8';
$config['newline'] = "\r\n";
$config['wordwrap'] = TRUE;

// Email addresses
$config['contact_email'] = '<EMAIL>'; // Where contact form emails are sent
$config['from_email'] = '<EMAIL>'; // From address for system emails
$config['from_name'] = 'Alyanabea Real Estate';

/*
|--------------------------------------------------------------------------
| SMTP Configuration Instructions
|--------------------------------------------------------------------------
|
| To configure email sending, you need to:
|
| 1. For Gmail:
|    - Enable 2-factor authentication
|    - Generate an app password
|    - Use your Gmail address as smtp_user
|    - Use the app password as smtp_pass
|
| 2. For other email providers:
|    - Contact your hosting provider for SMTP settings
|    - Update smtp_host, smtp_port, smtp_user, smtp_pass accordingly
|
| 3. For cPanel hosting:
|    - smtp_host: mail.yourdomain.com
|    - smtp_port: 587 or 465
|    - smtp_user: <EMAIL>
|    - smtp_pass: your-email-password
|    - smtp_crypto: 'tls' for port 587, 'ssl' for port 465
|
*/
