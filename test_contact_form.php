<?php
// Test Contact Form Database Connection
echo "<h1>🔍 Contact Form Database Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// Include CodeIgniter database config
if (file_exists('application/config/database.php')) {
    include 'application/config/database.php';
    
    if (isset($db['default'])) {
        $host = $db['default']['hostname'];
        $username = $db['default']['username'];
        $password = $db['default']['password'];
        $database = $db['default']['database'];
        
        echo "<h2>1. Database Connection Test</h2>";
        echo "Host: <strong>$host</strong><br>";
        echo "Database: <strong>$database</strong><br>";
        echo "Username: <strong>$username</strong><br>";
        
        try {
            $connection = new mysqli($host, $username, $password, $database);
            
            if ($connection->connect_error) {
                echo "<span class='error'>❌ Database connection failed: " . $connection->connect_error . "</span><br>";
                exit;
            } else {
                echo "<span class='success'>✅ Database connected successfully</span><br>";
            }
            
            echo "<h2>2. Contacts Table Check</h2>";
            
            // Check if contacts table exists
            $table_check = $connection->query("SHOW TABLES LIKE 'contacts'");
            
            if ($table_check->num_rows > 0) {
                echo "<span class='success'>✅ Contacts table exists</span><br>";
                
                // Check table structure
                $structure = $connection->query("DESCRIBE contacts");
                echo "<h3>Table Structure:</h3>";
                echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
                echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
                
                while ($row = $structure->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . $row['Field'] . "</td>";
                    echo "<td>" . $row['Type'] . "</td>";
                    echo "<td>" . $row['Null'] . "</td>";
                    echo "<td>" . $row['Key'] . "</td>";
                    echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Count existing records
                $count_result = $connection->query("SELECT COUNT(*) as total FROM contacts");
                $count = $count_result->fetch_assoc()['total'];
                echo "<br><span class='info'>📊 Total contact records: <strong>$count</strong></span><br>";
                
                // Show recent records
                if ($count > 0) {
                    echo "<h3>Recent Contact Submissions:</h3>";
                    $recent = $connection->query("SELECT name, email, subject, created_at FROM contacts ORDER BY created_at DESC LIMIT 5");
                    
                    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
                    echo "<tr><th>Name</th><th>Email</th><th>Subject</th><th>Date</th></tr>";
                    
                    while ($row = $recent->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['email']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['subject']) . "</td>";
                        echo "<td>" . $row['created_at'] . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
                
            } else {
                echo "<span class='error'>❌ Contacts table does NOT exist</span><br>";
                echo "<strong>This is why your contact form isn't working!</strong><br><br>";
                
                echo "<div style='background:#f8d7da; padding:15px; border:1px solid #f5c6cb; border-radius:5px;'>";
                echo "<strong>🔧 To Fix This:</strong><br>";
                echo "1. Run the SQL script: <code>create_contacts_table.sql</code><br>";
                echo "2. Or manually create the contacts table in phpMyAdmin<br>";
                echo "3. Then test the contact form again<br>";
                echo "</div>";
            }
            
            echo "<h2>3. Test Contact Form Submission</h2>";
            
            if ($_POST['test_submit'] ?? false) {
                if ($table_check->num_rows > 0) {
                    // Test inserting a record
                    $test_data = [
                        'name' => 'Test User',
                        'email' => '<EMAIL>',
                        'phone' => '+966501234567',
                        'subject' => 'Test Submission',
                        'message' => 'This is a test message from the diagnostic script.',
                        'language' => 'en',
                        'status' => 'new',
                        'ip_address' => $_SERVER['REMOTE_ADDR'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    $fields = implode(', ', array_keys($test_data));
                    $values = "'" . implode("', '", array_values($test_data)) . "'";
                    $sql = "INSERT INTO contacts ($fields) VALUES ($values)";
                    
                    if ($connection->query($sql)) {
                        echo "<span class='success'>✅ Test contact submission successful!</span><br>";
                        echo "Insert ID: " . $connection->insert_id . "<br>";
                    } else {
                        echo "<span class='error'>❌ Test submission failed: " . $connection->error . "</span><br>";
                        echo "SQL: " . $sql . "<br>";
                    }
                } else {
                    echo "<span class='error'>❌ Cannot test - contacts table doesn't exist</span><br>";
                }
            }
            
            $connection->close();
            
        } catch (Exception $e) {
            echo "<span class='error'>❌ Error: " . $e->getMessage() . "</span><br>";
        }
        
    } else {
        echo "<span class='error'>❌ Database configuration not found</span><br>";
    }
} else {
    echo "<span class='error'>❌ Database config file not found</span><br>";
}

echo "<h2>4. Test Form Submission</h2>";
?>

<form method="post" style="background:#f0f0f0; padding:15px; border-radius:5px;">
    <p><strong>Test if database insertion works:</strong></p>
    <input type="hidden" name="test_submit" value="1">
    <input type="submit" value="Test Contact Submission" style="background:#007bff; color:white; padding:10px 20px; border:none; border-radius:5px;">
</form>

<h2>5. Next Steps</h2>
<div style="background:#e8f5e8; padding:15px; border-left:4px solid #4CAF50;">
    <strong>If contacts table doesn't exist:</strong><br>
    1. Run the SQL script: <code>create_contacts_table.sql</code> in phpMyAdmin<br>
    2. Or copy and paste the SQL from the file into phpMyAdmin<br>
    3. Refresh this page to verify the table was created<br>
    4. Test the contact form on your website<br><br>
    
    <strong>If table exists but form still doesn't work:</strong><br>
    1. Check CodeIgniter error logs in <code>application/logs/</code><br>
    2. Enable error reporting in <code>index.php</code><br>
    3. Check browser console for JavaScript errors<br>
    4. Verify form validation is passing<br>
</div>

<h2>6. Quick Links</h2>
<p>
    <a href="<?php echo str_replace('test_contact_form.php', '', $_SERVER['REQUEST_URI']); ?>اتصل-بنا" target="_blank">Test Contact Form (Arabic)</a> |
    <a href="<?php echo str_replace('test_contact_form.php', '', $_SERVER['REQUEST_URI']); ?>en/contact" target="_blank">Test Contact Form (English)</a>
</p>
