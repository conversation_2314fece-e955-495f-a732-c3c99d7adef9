<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Contact Form Message</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #4CAF50;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f9f9f9;
            padding: 30px;
            border: 1px solid #ddd;
        }
        .footer {
            background: #333;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
        }
        .field {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .field-label {
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        .field-value {
            color: #333;
        }
        .message-content {
            background: white;
            padding: 20px;
            border-radius: 5px;
            border: 1px solid #ddd;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Contact Form Message</h1>
        <p>Alyanabea Real Estate</p>
    </div>
    
    <div class="content">
        <p><strong>You have received a new message from your website contact form.</strong></p>
        
        <div class="field">
            <div class="field-label">Name:</div>
            <div class="field-value"><?php echo htmlspecialchars($name); ?></div>
        </div>
        
        <div class="field">
            <div class="field-label">Email:</div>
            <div class="field-value"><?php echo htmlspecialchars($email); ?></div>
        </div>
        
        <div class="field">
            <div class="field-label">Phone:</div>
            <div class="field-value"><?php echo htmlspecialchars($phone); ?></div>
        </div>
        
        <div class="field">
            <div class="field-label">Subject:</div>
            <div class="field-value"><?php echo htmlspecialchars($subject); ?></div>
        </div>
        
        <div class="field">
            <div class="field-label">Message:</div>
            <div class="message-content">
                <?php echo nl2br(htmlspecialchars($message)); ?>
            </div>
        </div>
        
        <div class="field">
            <div class="field-label">Submitted:</div>
            <div class="field-value"><?php echo date('F j, Y \a\t g:i A', strtotime($created_at)); ?></div>
        </div>
        
        <div class="field">
            <div class="field-label">Language:</div>
            <div class="field-value"><?php echo ucfirst($language); ?></div>
        </div>
    </div>
    
    <div class="footer">
        <p>This email was sent from the Alyanabea website contact form.</p>
        <p>Please reply directly to the customer's email: <?php echo htmlspecialchars($email); ?></p>
    </div>
</body>
</html>
