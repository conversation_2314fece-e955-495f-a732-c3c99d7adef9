<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class About extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Staff_model');
    }

    public function index()
    {
        // Get team members
        $data['team_members'] = $this->Staff_model->get_all_staff();

        // Company information
        $data['company_info'] = array(
            'founded_year' => 1993,
            'experience_years' => 30,
            'specialties' => array(
                'property_management',
                'buy_sell',
                'property_marketing',
                'maintenance'
            )
        );

        // Page meta data
        $data['page_title'] = $this->lang->line('about_us') . ' - ' . $this->lang->line('site_title');
        $data['meta_description'] = $this->lang->line('meta_description_about');
        $data['page_class'] = 'about-page';

        // Load views
        $this->load_view('templates/header', $data);
        $this->load_view('about/index', $data);
        $this->load_view('templates/footer', $data);
    }
}
