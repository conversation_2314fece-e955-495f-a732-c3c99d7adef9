<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Setup extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        echo "<h1>Database Setup</h1>";
        echo "<p>This page will help you set up the database for the Alyanabea website.</p>";
        echo "<h2>Database Configuration</h2>";
        echo "<p>Database: " . $this->db->database . "</p>";
        echo "<p>Host: " . $this->db->hostname . "</p>";
        echo "<p>Username: " . $this->db->username . "</p>";
        
        echo "<h2>Connection Test</h2>";
        
        // Test database connection
        if ($this->db->initialize()) {
            echo "<p style='color: green;'>✓ Database connection successful</p>";
            
            // Check if database exists
            if ($this->db->db_select()) {
                echo "<p style='color: green;'>✓ Database '{$this->db->database}' exists</p>";
                
                // Check if properties table exists
                if ($this->db->table_exists('properties')) {
                    echo "<p style='color: green;'>✓ Properties table exists</p>";
                    
                    // Count records
                    $count = $this->db->count_all('properties');
                    echo "<p>Properties table has {$count} records</p>";
                } else {
                    echo "<p style='color: red;'>✗ Properties table does not exist</p>";
                    echo "<p><a href='" . site_url('setup/create_tables') . "'>Create Tables</a></p>";
                }
            } else {
                echo "<p style='color: red;'>✗ Database '{$this->db->database}' does not exist</p>";
                echo "<p>Please create the database manually in phpMyAdmin or MySQL</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Database connection failed</p>";
            echo "<p>Please check your database configuration in application/config/database.php</p>";
        }
        
        echo "<h2>Instructions</h2>";
        echo "<ol>";
        echo "<li>Make sure XAMPP is running with MySQL service started</li>";
        echo "<li>Open phpMyAdmin (http://localhost/phpmyadmin)</li>";
        echo "<li>Create a new database named 'alyanabe_webart'</li>";
        echo "<li>Import the SQL file from 'database/alyanabea_db.sql'</li>";
        echo "<li>Refresh this page to test the connection</li>";
        echo "</ol>";
    }

    public function create_tables()
    {
        echo "<h1>Creating Database Tables</h1>";
        
        // Read the SQL file
        $sql_file = FCPATH . 'database/alyanabea_db.sql';
        
        if (!file_exists($sql_file)) {
            echo "<p style='color: red;'>✗ SQL file not found: {$sql_file}</p>";
            return;
        }
        
        $sql_content = file_get_contents($sql_file);
        
        if (empty($sql_content)) {
            echo "<p style='color: red;'>✗ SQL file is empty</p>";
            return;
        }
        
        // Split SQL statements
        $statements = explode(';', $sql_content);
        
        $success_count = 0;
        $error_count = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            try {
                if ($this->db->query($statement)) {
                    $success_count++;
                    echo "<p style='color: green;'>✓ Executed: " . substr($statement, 0, 50) . "...</p>";
                } else {
                    $error_count++;
                    echo "<p style='color: red;'>✗ Failed: " . substr($statement, 0, 50) . "...</p>";
                }
            } catch (Exception $e) {
                $error_count++;
                echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<h2>Summary</h2>";
        echo "<p>Successful statements: {$success_count}</p>";
        echo "<p>Failed statements: {$error_count}</p>";
        
        if ($error_count == 0) {
            echo "<p style='color: green;'>✓ Database setup completed successfully!</p>";
            echo "<p><a href='" . site_url() . "'>Go to Homepage</a></p>";
        } else {
            echo "<p style='color: orange;'>⚠ Some errors occurred. Please check manually.</p>";
        }
        
        echo "<p><a href='" . site_url('setup') . "'>Back to Setup</a></p>";
    }
}
