<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Language extends CI_Controller {

    public function switch_language($language = 'arabic')
    {
        // Get supported languages
        $supported_languages = $this->config->item('supported_languages');

        // Validate language
        if (!array_key_exists($language, $supported_languages)) {
            $language = $this->config->item('default_language');
        }

        // Set session
        $this->session->set_userdata('language', $language);

        // Log language switch for debugging
        log_message('info', 'Language switched to: ' . $language);

        // Get referrer or redirect to home
        $referrer = $this->input->server('HTTP_REFERER');

        if ($referrer && strpos($referrer, base_url()) === 0) {
            // Parse referrer to get the path
            $parsed_url = parse_url($referrer);
            $path = isset($parsed_url['path']) ? $parsed_url['path'] : '';

            // Remove base path from URL
            $base_path = parse_url(base_url(), PHP_URL_PATH);
            if ($base_path && $base_path !== '/' && strpos($path, $base_path) === 0) {
                $path = substr($path, strlen($base_path));
            } elseif ($base_path === '/') {
                $path = ltrim($path, '/');
            }

            // URL decode the path to handle encoded Arabic characters
            $path = urldecode($path);

            // Remove language prefix if exists
            if (strpos($path, 'en/') === 0) {
                $path = substr($path, 3);
            }

            // Handle Arabic route mappings (after URL decoding)
            $arabic_to_english = array(
                'الرئيسية' => '',
                'العروض-العقارية' => 'advertisements',
                'من-نحن' => 'about',
                'اتصل-بنا' => 'contact',
                'الموظفين' => 'staff'
            );

            $english_to_arabic = array(
                '' => 'الرئيسية',
                'advertisements' => 'العروض-العقارية',
                'about' => 'من-نحن',
                'contact' => 'اتصل-بنا',
                'staff' => 'الموظفين'
            );

            // Convert path based on target language
            if ($language === 'english') {
                // Convert Arabic path to English
                foreach ($arabic_to_english as $arabic => $english) {
                    if ($path === $arabic) {
                        $path = $english;
                        break;
                    }
                    // Handle property/staff detail pages
                    if (strpos($path, $arabic . '/') === 0) {
                        $path = str_replace($arabic . '/', $english . '/', $path);
                        break;
                    }
                }
                $redirect_url = base_url('en/' . $path);
            } else {
                // Convert English path to Arabic
                foreach ($english_to_arabic as $english => $arabic) {
                    if ($path === $english) {
                        $path = $arabic;
                        break;
                    }
                    // Handle property/staff detail pages
                    if (strpos($path, $english . '/') === 0) {
                        $path = str_replace($english . '/', $arabic . '/', $path);
                        break;
                    }
                }
                $redirect_url = base_url($path);
            }

            redirect($redirect_url);
        } else {
            // Redirect to home
            if ($language === 'english') {
                redirect(base_url('en'));
            } else {
                redirect(base_url());
            }
        }
    }

    public function test()
    {
        echo "<h1>Language Switch Test</h1>";
        echo "<p>Current Language: " . $this->session->userdata('language') . "</p>";
        echo "<p>Config Language: " . $this->config->item('language') . "</p>";
        echo "<p>Current URI: " . uri_string() . "</p>";
        echo "<p>Base URL: " . base_url() . "</p>";
        echo "<p>Referrer: " . $this->input->server('HTTP_REFERER') . "</p>";

        echo "<h2>Language Switch Links:</h2>";
        echo "<a href='" . base_url('lang/arabic') . "'>Switch to Arabic</a><br>";
        echo "<a href='" . base_url('lang/english') . "'>Switch to English</a><br>";

        echo "<h2>Session Data:</h2>";
        echo "<pre>";
        print_r($this->session->all_userdata());
        echo "</pre>";
    }
}
