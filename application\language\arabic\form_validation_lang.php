<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Arabic Language File for Form Validation
 * 
 * This file contains Arabic translations for CodeIgniter's form validation messages
 */

$lang['form_validation_required']		= 'حقل {field} مطلوب.';
$lang['form_validation_isset']			= 'حقل {field} يجب أن يحتوي على قيمة.';
$lang['form_validation_valid_email']		= 'حقل {field} يجب أن يحتوي على عنوان بريد إلكتروني صحيح.';
$lang['form_validation_valid_emails']		= 'حقل {field} يجب أن يحتوي على عناوين بريد إلكتروني صحيحة.';
$lang['form_validation_valid_url']		= 'حقل {field} يجب أن يحتوي على رابط صحيح.';
$lang['form_validation_valid_ip']		= 'حقل {field} يجب أن يحتوي على عنوان IP صحيح.';
$lang['form_validation_valid_base64']		= 'حقل {field} يجب أن يحتوي على نص Base64 صحيح.';
$lang['form_validation_min_length']		= 'حقل {field} يجب أن يكون على الأقل {param} أحرف في الطول.';
$lang['form_validation_max_length']		= 'حقل {field} لا يمكن أن يتجاوز {param} أحرف في الطول.';
$lang['form_validation_exact_length']		= 'حقل {field} يجب أن يكون بالضبط {param} أحرف في الطول.';
$lang['form_validation_alpha']			= 'حقل {field} يمكن أن يحتوي فقط على أحرف أبجدية.';
$lang['form_validation_alpha_numeric']		= 'حقل {field} يمكن أن يحتوي فقط على أحرف أبجدية ورقمية.';
$lang['form_validation_alpha_numeric_spaces']	= 'حقل {field} يمكن أن يحتوي فقط على أحرف أبجدية ورقمية ومسافات.';
$lang['form_validation_alpha_dash']		= 'حقل {field} يمكن أن يحتوي فقط على أحرف أبجدية ورقمية وشرطات سفلية وشرطات.';
$lang['form_validation_numeric']		= 'حقل {field} يجب أن يحتوي فقط على أرقام.';
$lang['form_validation_is_numeric']		= 'حقل {field} يجب أن يحتوي فقط على أحرف رقمية.';
$lang['form_validation_integer']		= 'حقل {field} يجب أن يحتوي على عدد صحيح.';
$lang['form_validation_regex_match']		= 'حقل {field} ليس في التنسيق الصحيح.';
$lang['form_validation_matches']		= 'حقل {field} لا يطابق حقل {param}.';
$lang['form_validation_differs']		= 'حقل {field} يجب أن يختلف عن حقل {param}.';
$lang['form_validation_is_unique'] 		= 'حقل {field} يجب أن يحتوي على قيمة فريدة.';
$lang['form_validation_is_natural']		= 'حقل {field} يجب أن يحتوي فقط على أرقام.';
$lang['form_validation_is_natural_no_zero']	= 'حقل {field} يجب أن يحتوي فقط على أرقام ويجب أن يكون أكبر من الصفر.';
$lang['form_validation_decimal']		= 'حقل {field} يجب أن يحتوي على رقم عشري.';
$lang['form_validation_less_than']		= 'حقل {field} يجب أن يحتوي على رقم أقل من {param}.';
$lang['form_validation_less_than_equal_to']	= 'حقل {field} يجب أن يحتوي على رقم أقل من أو يساوي {param}.';
$lang['form_validation_greater_than']		= 'حقل {field} يجب أن يحتوي على رقم أكبر من {param}.';
$lang['form_validation_greater_than_equal_to']	= 'حقل {field} يجب أن يحتوي على رقم أكبر من أو يساوي {param}.';
$lang['form_validation_error_message_not_set']	= 'غير قادر على الوصول إلى رسالة خطأ مطابقة لاسم الحقل {field}.';
$lang['form_validation_in_list']		= 'حقل {field} يجب أن يكون واحداً من: {param}.';
