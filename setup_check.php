<?php
/**
 * <PERSON><PERSON> Al-Yanabea Website Setup Verification
 * This script helps verify that your installation is correct
 */

// Check if we're running from the correct directory
if (!file_exists('index.php') || !file_exists('application/config/config.php')) {
    die('Error: Please run this script from the root directory of your project.');
}

echo "<h1><PERSON>wan Al-Yanabea Website Setup Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

// 1. Check PHP Version
echo "<div class='section'>";
echo "<h2>1. PHP Version Check</h2>";
$php_version = phpversion();
if (version_compare($php_version, '7.0.0', '>=')) {
    echo "<span class='success'>✓ PHP Version: $php_version (Good)</span>";
} else {
    echo "<span class='error'>✗ PHP Version: $php_version (Requires PHP 7.0+)</span>";
}
echo "</div>";

// 2. Check Required Extensions
echo "<div class='section'>";
echo "<h2>2. PHP Extensions Check</h2>";
$required_extensions = ['mysqli', 'mbstring', 'json', 'session'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span class='success'>✓ $ext extension loaded</span><br>";
    } else {
        echo "<span class='error'>✗ $ext extension missing</span><br>";
    }
}
echo "</div>";

// 3. Check File Structure
echo "<div class='section'>";
echo "<h2>3. File Structure Check</h2>";
$required_files = [
    'system' => 'CodeIgniter 3 system folder',
    'application/config/config.php' => 'Main configuration',
    'application/config/database.php' => 'Database configuration',
    'application/controllers/Home.php' => 'Home controller',
    'application/views/templates/header.php' => 'Header template',
    'assets/css/style.css' => 'Main stylesheet',
    'assets/js/main.js' => 'Main JavaScript'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<span class='success'>✓ $file ($description)</span><br>";
    } else {
        echo "<span class='error'>✗ $file missing ($description)</span><br>";
    }
}
echo "</div>";

// 4. Check Directory Permissions
echo "<div class='section'>";
echo "<h2>4. Directory Permissions Check</h2>";
$writable_dirs = [
    'application/logs',
    'assets/images'
];

foreach ($writable_dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<span class='success'>✓ $dir is writable</span><br>";
        } else {
            echo "<span class='warning'>⚠ $dir exists but not writable</span><br>";
        }
    } else {
        echo "<span class='error'>✗ $dir directory missing</span><br>";
    }
}
echo "</div>";

// 5. Check Configuration
echo "<div class='section'>";
echo "<h2>5. Configuration Check</h2>";

// Check base URL
if (file_exists('application/config/config.php')) {
    include 'application/config/config.php';
    if (isset($config['base_url']) && !empty($config['base_url'])) {
        echo "<span class='success'>✓ Base URL configured: " . $config['base_url'] . "</span><br>";
    } else {
        echo "<span class='warning'>⚠ Base URL not configured</span><br>";
    }
}

// Check database config
if (file_exists('application/config/database.php')) {
    include 'application/config/database.php';
    if (isset($db['default']['database']) && !empty($db['default']['database'])) {
        echo "<span class='success'>✓ Database configured: " . $db['default']['database'] . "</span><br>";
    } else {
        echo "<span class='warning'>⚠ Database not configured</span><br>";
    }
}
echo "</div>";

// 6. Database Connection Test
echo "<div class='section'>";
echo "<h2>6. Database Connection Test</h2>";
if (isset($db['default'])) {
    $connection = @mysqli_connect(
        $db['default']['hostname'],
        $db['default']['username'],
        $db['default']['password'],
        $db['default']['database']
    );
    
    if ($connection) {
        echo "<span class='success'>✓ Database connection successful</span><br>";
        
        // Check if tables exist
        $tables = ['properties', 'contacts', 'staff'];
        foreach ($tables as $table) {
            $result = @mysqli_query($connection, "SHOW TABLES LIKE '$table'");
            if ($result && mysqli_num_rows($result) > 0) {
                echo "<span class='success'>✓ Table '$table' exists</span><br>";
            } else {
                echo "<span class='error'>✗ Table '$table' missing</span><br>";
            }
        }
        
        mysqli_close($connection);
    } else {
        echo "<span class='error'>✗ Database connection failed: " . mysqli_connect_error() . "</span><br>";
    }
} else {
    echo "<span class='error'>✗ Database configuration not found</span><br>";
}
echo "</div>";

// 7. URL Testing
echo "<div class='section'>";
echo "<h2>7. URL Access Test</h2>";
$current_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']);
echo "<span class='info'>Current URL: $current_url</span><br>";
echo "<span class='info'>Try accessing these URLs:</span><br>";
echo "• <a href='$current_url/index.php' target='_blank'>$current_url/index.php</a> (Main site)<br>";
echo "• <a href='$current_url/index.php/en' target='_blank'>$current_url/index.php/en</a> (English version)<br>";
echo "• <a href='$current_url/index.php/العروض-العقارية' target='_blank'>$current_url/index.php/العروض-العقارية</a> (Arabic properties)<br>";
echo "</div>";

// 8. Next Steps
echo "<div class='section'>";
echo "<h2>8. Next Steps</h2>";
echo "<ol>";
echo "<li><strong>If all checks pass:</strong> Your installation is ready! Access your website using the URLs above.</li>";
echo "<li><strong>If database connection fails:</strong> Check your database credentials in application/config/database.php</li>";
echo "<li><strong>If tables are missing:</strong> Import the SQL file: database/alyanabea_db.sql</li>";
echo "<li><strong>If files are missing:</strong> Make sure you've copied the CodeIgniter 3 'system' folder</li>";
echo "<li><strong>If permissions are wrong:</strong> Set proper write permissions on the specified directories</li>";
echo "</ol>";
echo "</div>";

// 9. Troubleshooting
echo "<div class='section'>";
echo "<h2>9. Common Issues & Solutions</h2>";
echo "<ul>";
echo "<li><strong>Blank page:</strong> Check PHP error logs, ensure system folder exists</li>";
echo "<li><strong>404 errors:</strong> Check .htaccess file, ensure mod_rewrite is enabled</li>";
echo "<li><strong>Database errors:</strong> Verify database exists and credentials are correct</li>";
echo "<li><strong>Language not switching:</strong> Check session configuration and language files</li>";
echo "<li><strong>CSS/JS not loading:</strong> Check file paths and permissions</li>";
echo "</ul>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>10. Support</h2>";
echo "<p>If you encounter issues:</p>";
echo "<ul>";
echo "<li>Check the README.md file for detailed instructions</li>";
echo "<li>Verify all requirements are met</li>";
echo "<li>Check server error logs</li>";
echo "<li>Ensure proper file permissions</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><em>Setup check completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
