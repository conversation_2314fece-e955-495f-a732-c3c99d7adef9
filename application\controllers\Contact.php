<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Contact extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Contact_model');
        $this->load->library('email');
    }

    public function index()
    {
        $data['success'] = false;
        $data['error'] = false;

        // Handle form submission
        if ($this->input->post()) {
            $this->form_validation->set_rules('name', $this->lang->line('your_name'), 'required|trim|min_length[2]|max_length[100]');
            $this->form_validation->set_rules('email', $this->lang->line('your_email'), 'required|valid_email|trim');
            $this->form_validation->set_rules('phone', $this->lang->line('your_phone'), 'required|trim|min_length[10]|max_length[20]');
            $this->form_validation->set_rules('subject', $this->lang->line('subject'), 'required|trim|min_length[5]|max_length[200]');
            $this->form_validation->set_rules('message', $this->lang->line('message'), 'required|trim|min_length[10]|max_length[1000]');

            if ($this->form_validation->run()) {
                $contact_data = array(
                    'name' => $this->input->post('name'),
                    'email' => $this->input->post('email'),
                    'phone' => $this->input->post('phone'),
                    'subject' => $this->input->post('subject'),
                    'message' => $this->input->post('message'),
                    'language' => $this->current_language,
                    'created_at' => date('Y-m-d H:i:s')
                );

                if ($this->Contact_model->save_contact($contact_data)) {
                    // Send email notification
                    if ($this->send_contact_email($contact_data)) {
                        $data['success'] = true;
                        $this->session->set_flashdata('success', $this->lang->line('success_message'));
                        redirect(current_url());
                    } else {
                        // Data saved but email failed
                        $this->session->set_flashdata('success', $this->lang->line('message_saved_email_failed'));
                        redirect(current_url());
                    }
                } else {
                    $data['error'] = $this->lang->line('error_message');
                }
            }
        }

        // Company contact information
        $data['contact_info'] = array(
            'address' => $this->lang->line('company_address'),
            'phone' => $this->lang->line('company_phone'),
            'mobile' => $this->lang->line('company_mobile'),
            'email' => $this->lang->line('company_email'),
            'working_hours' => array(
                'saturday_thursday' => '8:00 AM - 6:00 PM',
                'friday' => 'Closed'
            )
        );

        // Page meta data
        $data['page_title'] = $this->lang->line('nav_contact') . ' - ' . $this->lang->line('site_title');
        $data['meta_description'] = $this->lang->line('meta_description_contact');
        $data['page_class'] = 'contact-page';

        // Load views
        $this->load_view('templates/header', $data);
        $this->load_view('contact/index', $data);
        $this->load_view('templates/footer', $data);
    }

    /**
     * Send contact form <NAME_EMAIL>
     */
    private function send_contact_email($contact_data)
    {
        try {
            // Load email configuration
            $this->config->load('email');
            $email_config = $this->config->item('email');

            // Initialize email with configuration
            $this->email->initialize($email_config);

            // Email content
            $this->email->from($contact_data['email'], $contact_data['name']);
            $this->email->reply_to($contact_data['email'], $contact_data['name']);
            $this->email->to($this->config->item('contact_email'));
            $this->email->subject('New Contact Form Message: ' . $contact_data['subject']);

            // Email body
            $message = $this->load->view('emails/contact_form', $contact_data, TRUE);
            $this->email->message($message);

            // Send email
            if ($this->email->send()) {
                return true;
            } else {
                log_message('error', 'Email sending failed: ' . $this->email->print_debugger());
                return false;
            }
        } catch (Exception $e) {
            log_message('error', 'Email exception: ' . $e->getMessage());
            return false;
        }
    }
}
