<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Contact extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Contact_model');
        $this->load->library('email');
    }

    public function index()
    {
        $data['success'] = false;
        $data['error'] = false;

        // Debug: Log form submission attempt
        if ($this->input->post()) {
            log_message('info', 'Contact form submission received: ' . json_encode($this->input->post()));

            // Also check if this is the debug form
            if (!$this->input->post('test_submit')) {
                log_message('info', 'This is the ACTUAL contact form submission (not debug)');
            }
        }

        // Handle form submission
        if ($this->input->post() && !$this->input->post('test_submit')) {
            $this->form_validation->set_rules('name', $this->lang->line('your_name'), 'required|trim|min_length[2]|max_length[100]');
            $this->form_validation->set_rules('email', $this->lang->line('your_email'), 'required|valid_email|trim');
            $this->form_validation->set_rules('phone', $this->lang->line('your_phone'), 'required|trim|min_length[10]|max_length[20]');
            $this->form_validation->set_rules('subject', $this->lang->line('subject'), 'required|trim|min_length[5]|max_length[200]');
            $this->form_validation->set_rules('message', $this->lang->line('message'), 'required|trim|min_length[10]|max_length[1000]');

            if ($this->form_validation->run()) {
                log_message('info', 'Contact form validation passed');
                $contact_data = array(
                    'name' => $this->input->post('name'),
                    'email' => $this->input->post('email'),
                    'phone' => $this->input->post('phone'),
                    'subject' => $this->input->post('subject'),
                    'message' => $this->input->post('message'),
                    'language' => $this->current_language,
                    'created_at' => date('Y-m-d H:i:s')
                );

                // Add debugging and better error handling
                $save_result = $this->Contact_model->save_contact($contact_data);

                if ($save_result) {
                    // Send email notification
                    if ($this->send_contact_email($contact_data)) {
                        $this->session->set_flashdata('success', 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.');
                        redirect(current_url());
                    } else {
                        // Data saved but email failed
                        $this->session->set_flashdata('success', 'تم حفظ رسالتك بنجاح. سنتواصل معك قريباً.');
                        redirect(current_url());
                    }
                } else {
                    // Log database error for debugging
                    log_message('error', 'Contact form save failed: ' . $this->db->last_query());
                    log_message('error', 'Database error: ' . $this->db->error()['message']);
                    $data['error'] = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.';
                }
            } else {
                // Log validation errors
                log_message('error', 'Contact form validation failed: ' . validation_errors());
                $data['error'] = 'يرجى التأكد من ملء جميع الحقول المطلوبة بشكل صحيح.';
            }
        }

        // Company contact information
        $data['contact_info'] = array(
            'address' => $this->lang->line('company_address'),
            'phone' => $this->lang->line('company_phone'),
            'mobile' => $this->lang->line('company_mobile'),
            'email' => $this->lang->line('company_email'),
            'working_hours' => array(
                'saturday_thursday' => '8:00 AM - 6:00 PM',
                'friday' => 'Closed'
            )
        );

        // Page meta data
        $data['page_title'] = $this->lang->line('nav_contact') . ' - ' . $this->lang->line('site_title');
        $data['meta_description'] = $this->lang->line('meta_description_contact');
        $data['page_class'] = 'contact-page';

        // Load views
        $this->load_view('templates/header', $data);
        $this->load_view('contact/index', $data);
        $this->load_view('templates/footer', $data);
    }

    /**
     * Send contact form <NAME_EMAIL>
     */
    private function send_contact_email($contact_data)
    {
        try {
            // Load email configuration
            $this->config->load('email');
            $email_config = $this->config->item('email');

            // Initialize email with configuration
            $this->email->initialize($email_config);

            // Email content
            $this->email->from($contact_data['email'], $contact_data['name']);
            $this->email->reply_to($contact_data['email'], $contact_data['name']);
            $this->email->to($this->config->item('contact_email'));
            $this->email->subject('New Contact Form Message: ' . $contact_data['subject']);

            // Email body
            $message = $this->load->view('emails/contact_form', $contact_data, TRUE);
            $this->email->message($message);

            // Send email
            if ($this->email->send()) {
                return true;
            } else {
                log_message('error', 'Email sending failed: ' . $this->email->print_debugger());
                return false;
            }
        } catch (Exception $e) {
            log_message('error', 'Email exception: ' . $e->getMessage());
            return false;
        }
    }

    // Debug method to test contact form functionality
    public function debug()
    {
        echo "<h1>🔍 Contact Form Debug</h1>";
        echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

        echo "<h2>1. Database Connection Test</h2>";

        // Test database connection
        if ($this->db->conn_id) {
            echo "<span class='success'>✅ Database connected successfully</span><br>";
            echo "Database: <strong>" . $this->db->database . "</strong><br>";
        } else {
            echo "<span class='error'>❌ Database connection failed</span><br>";
            return;
        }

        echo "<h2>2. Contacts Table Check</h2>";

        // Check if contacts table exists
        if ($this->db->table_exists('contacts')) {
            echo "<span class='success'>✅ Contacts table exists</span><br>";

            // Count existing records
            $count = $this->db->count_all('contacts');
            echo "<br><span class='info'>📊 Total contact records: <strong>$count</strong></span><br>";

        } else {
            echo "<span class='error'>❌ Contacts table does NOT exist</span><br>";
            echo "<div style='background:#f8d7da; padding:15px; border:1px solid #f5c6cb; border-radius:5px; margin:10px 0;'>";
            echo "<strong>🔧 This is why your contact form isn't working!</strong><br>";
            echo "You need to create the contacts table in your database.<br>";
            echo "</div>";
        }

        echo "<h2>3. Test Contact Form Submission</h2>";

        if ($this->input->post('test_submit')) {
            echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffeaa7; border-radius:5px; margin:10px 0;'>";
            echo "<strong>📝 Form Data Received:</strong><br>";
            foreach ($this->input->post() as $key => $value) {
                if ($key != 'test_submit') {
                    echo "<strong>$key:</strong> " . htmlspecialchars($value) . "<br>";
                }
            }
            echo "</div>";

            if ($this->db->table_exists('contacts')) {
                // Try to insert test data
                $test_data = array(
                    'name' => $this->input->post('name'),
                    'email' => $this->input->post('email'),
                    'phone' => $this->input->post('phone'),
                    'subject' => $this->input->post('subject'),
                    'message' => $this->input->post('message'),
                    'language' => 'en',
                    'status' => 'new',
                    'ip_address' => $this->input->ip_address(),
                    'created_at' => date('Y-m-d H:i:s')
                );

                if ($this->db->insert('contacts', $test_data)) {
                    echo "<span class='success'>✅ Test contact submission successful!</span><br>";
                    echo "Insert ID: <strong>" . $this->db->insert_id() . "</strong><br>";
                } else {
                    echo "<span class='error'>❌ Test submission failed</span><br>";
                    $error = $this->db->error();
                    echo "Error: " . $error['message'] . "<br>";
                    echo "Query: " . $this->db->last_query() . "<br>";
                }
            } else {
                echo "<span class='error'>❌ Cannot test - contacts table doesn't exist</span><br>";
            }
        }

        // Test form
        echo "<form method='post' style='background:#f8f9fa; padding:20px; border:1px solid #dee2e6; border-radius:5px; max-width:600px; margin:20px 0;'>";
        echo "<h3>Test Contact Form Submission</h3>";
        echo "<input type='hidden' name='test_submit' value='1'>";

        echo "<div style='margin-bottom:15px;'>";
        echo "<label>Name:</label><br>";
        echo "<input type='text' name='name' value='Test User' required style='width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;'>";
        echo "</div>";

        echo "<div style='margin-bottom:15px;'>";
        echo "<label>Email:</label><br>";
        echo "<input type='email' name='email' value='<EMAIL>' required style='width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;'>";
        echo "</div>";

        echo "<div style='margin-bottom:15px;'>";
        echo "<label>Phone:</label><br>";
        echo "<input type='tel' name='phone' value='+966501234567' required style='width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;'>";
        echo "</div>";

        echo "<div style='margin-bottom:15px;'>";
        echo "<label>Subject:</label><br>";
        echo "<input type='text' name='subject' value='Test Message' required style='width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;'>";
        echo "</div>";

        echo "<div style='margin-bottom:15px;'>";
        echo "<label>Message:</label><br>";
        echo "<textarea name='message' required style='width:100%; padding:8px; border:1px solid #ccc; border-radius:4px; height:100px;'>This is a test message to debug the contact form.</textarea>";
        echo "</div>";

        echo "<input type='submit' value='Test Submit' style='background:#007bff; color:white; padding:10px 20px; border:none; border-radius:5px; cursor:pointer;'>";
        echo "</form>";

        echo "<h2>4. Create Contacts Table</h2>";
        echo "<div style='background:#e8f5e8; padding:15px; border-left:4px solid #4CAF50;'>";
        echo "<strong>If the contacts table doesn't exist, run this SQL in phpMyAdmin:</strong><br><br>";
        echo "<textarea style='width:100%; height:200px; font-family:monospace; font-size:12px;'>";
        echo "CREATE TABLE contacts (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(100) NOT NULL,
  email varchar(100) NOT NULL,
  phone varchar(20) NOT NULL,
  subject varchar(200) NOT NULL,
  message text NOT NULL,
  language varchar(2) DEFAULT 'ar',
  status enum('new','read','replied','archived') DEFAULT 'new',
  ip_address varchar(45) DEFAULT NULL,
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        echo "</textarea>";
        echo "</div>";

        echo "<h2>5. Next Steps</h2>";
        echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffeaa7; border-radius:5px;'>";
        echo "<strong>To fix your contact form:</strong><br>";
        echo "1. <strong>Create the contacts table</strong> using the SQL above<br>";
        echo "2. <strong>Test this form</strong> to verify it works<br>";
        echo "3. <strong>Try the real contact form</strong> at <a href='" . base_url('اتصل-بنا') . "' target='_blank'>/اتصل-بنا</a><br>";
        echo "</div>";
    }

    // Debug method specifically for English contact route
    public function debug_english()
    {
        echo "<h1>🔍 English Contact Route Debug</h1>";
        echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

        echo "<h2>1. Language Detection Test</h2>";
        echo "Current Language: <strong>" . $this->current_language . "</strong><br>";
        echo "Is English: <strong>" . ($this->current_language === 'english' ? 'YES' : 'NO') . "</strong><br>";
        echo "URI String: <strong>" . uri_string() . "</strong><br>";
        echo "First URI Segment: <strong>" . $this->uri->segment(1) . "</strong><br>";

        echo "<h2>2. Route Test</h2>";
        echo "This debug page URL: <strong>" . current_url() . "</strong><br>";
        echo "English Contact URL should be: <strong>" . base_url('en/contact') . "</strong><br>";

        echo "<h2>3. Language File Test</h2>";
        echo "Contact Form Label: <strong>" . $this->lang->line('contact_form') . "</strong><br>";
        echo "Your Name Label: <strong>" . $this->lang->line('your_name') . "</strong><br>";
        echo "Send Message Label: <strong>" . $this->lang->line('send_message') . "</strong><br>";

        echo "<h2>4. Direct Link Tests</h2>";
        echo "<p><a href='" . base_url('en/contact') . "' target='_blank'>Test: " . base_url('en/contact') . "</a></p>";
        echo "<p><a href='" . base_url('اتصل-بنا') . "' target='_blank'>Test: " . base_url('اتصل-بنا') . " (Arabic)</a></p>";

        echo "<h2>5. Controller Test</h2>";
        echo "Contact Controller exists: <strong>" . (file_exists(APPPATH . 'controllers/Contact.php') ? 'YES' : 'NO') . "</strong><br>";
        echo "Contact View exists: <strong>" . (file_exists(APPPATH . 'views/contact/index.php') ? 'YES' : 'NO') . "</strong><br>";

        echo "<h2>6. Manual Contact Form Test</h2>";
        echo "<div style='background:#f8f9fa; padding:20px; border:1px solid #dee2e6; border-radius:5px; max-width:600px;'>";
        echo "<h3>Try accessing the English contact form:</h3>";
        echo "<p><strong>Method 1:</strong> <a href='" . base_url('en/contact') . "' target='_blank'>Direct Link: /en/contact</a></p>";
        echo "<p><strong>Method 2:</strong> <a href='" . base_url('contact') . "' target='_blank'>Without prefix: /contact</a></p>";
        echo "<p><strong>Method 3:</strong> <a href='" . base_url('index.php/contact') . "' target='_blank'>With index.php: /index.php/contact</a></p>";
        echo "</div>";

        echo "<h2>7. Error Checking</h2>";
        if (function_exists('error_get_last')) {
            $last_error = error_get_last();
            if ($last_error) {
                echo "<div style='background:#f8d7da; padding:15px; border:1px solid #f5c6cb; border-radius:5px;'>";
                echo "<strong>Last PHP Error:</strong><br>";
                echo "Message: " . $last_error['message'] . "<br>";
                echo "File: " . $last_error['file'] . "<br>";
                echo "Line: " . $last_error['line'] . "<br>";
                echo "</div>";
            } else {
                echo "<span class='success'>✅ No PHP errors detected</span><br>";
            }
        }

        echo "<h2>8. Next Steps</h2>";
        echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffeaa7; border-radius:5px;'>";
        echo "<strong>If /en/contact is not working:</strong><br>";
        echo "1. Try the direct links above<br>";
        echo "2. Check if you get 404 error or different error<br>";
        echo "3. Check web server error logs<br>";
        echo "4. Try clearing browser cache<br>";
        echo "5. Check .htaccess file for URL rewriting issues<br>";
        echo "</div>";
    }
}
