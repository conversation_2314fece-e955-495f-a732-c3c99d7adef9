# Database Setup Instructions

## Problem
You're getting the error: "Call to a member function result() on bool" because the database connection is failing or the database/tables don't exist.

## Solution Steps

### Step 1: Check XAMPP
1. Make sure XAMPP is running
2. Start the MySQL service in XAMPP Control Panel
3. Verify MySQL is running on port 3307 (as configured in your database.php)

### Step 2: Create Database
1. Open phpMyAdmin: http://localhost/phpmyadmin
2. Click "New" to create a new database
3. Name it: `alyanabe_webart` (exactly as in your config)
4. Set collation to: `utf8mb4_unicode_ci`
5. Click "Create"

### Step 3: Import Database Structure
1. Select the `alyanabe_webart` database you just created
2. Click the "Import" tab
3. Click "Choose File" and select: `database/alyanabea_db.sql`
4. Click "Go" to import

### Step 4: Verify Setup
1. Visit: http://localhost/alyanabea-website/index.php/setup
2. This will test your database connection and show any issues
3. If tables are missing, you can use the setup page to create them

### Step 5: Test Website
1. Visit: http://localhost/alyanabea-website/
2. The error should be resolved

## Alternative: Manual Database Creation

If the import doesn't work, you can create the database manually:

```sql
CREATE DATABASE alyanabe_webart CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE alyanabe_webart;

-- Then copy and paste the contents of database/alyanabea_db.sql
```

## Troubleshooting

### If you get "Access denied" error:
- Check your MySQL username/password in `application/config/database.php`
- Default XAMPP MySQL credentials are usually:
  - Username: `root`
  - Password: (empty)

### If you get "Database does not exist" error:
- Make sure you created the database with the exact name: `alyanabe_webart`
- Check the database name in `application/config/database.php`

### If you get "Table doesn't exist" error:
- Make sure you imported the SQL file completely
- Check that all tables were created in phpMyAdmin

## Current Database Configuration
- Host: localhost:3307
- Database: alyanabe_webart
- Username: root
- Password: (empty)

## Files Modified
I've added error handling to prevent fatal errors in:
- `application/models/Property_model.php` - All methods now handle database failures gracefully
- `application/config/database.php` - Enabled debug mode temporarily
- `application/controllers/Setup.php` - New setup controller for testing

The website should now show empty content instead of fatal errors if the database is not set up.
