<?php
// Image Upload Diagnostic Script
echo "<h2>Image Upload Diagnostic</h2>";

// Check directory permissions
$upload_dir = './assets/images/properties/';
echo "<h3>Directory Check:</h3>";
echo "Upload directory: " . $upload_dir . "<br>";
echo "Directory exists: " . (is_dir($upload_dir) ? "YES" : "NO") . "<br>";
echo "Directory writable: " . (is_writable($upload_dir) ? "YES" : "NO") . "<br>";
echo "Directory permissions: " . substr(sprintf('%o', fileperms($upload_dir)), -4) . "<br>";

// Check parent directory permissions
$parent_dir = './assets/images/';
echo "<br><h3>Parent Directory Check:</h3>";
echo "Parent directory: " . $parent_dir . "<br>";
echo "Parent exists: " . (is_dir($parent_dir) ? "YES" : "NO") . "<br>";
echo "Parent writable: " . (is_writable($parent_dir) ? "YES" : "NO") . "<br>";
echo "Parent permissions: " . substr(sprintf('%o', fileperms($parent_dir)), -4) . "<br>";

// Check PHP upload settings
echo "<br><h3>PHP Upload Settings:</h3>";
echo "file_uploads: " . (ini_get('file_uploads') ? "ON" : "OFF") . "<br>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
echo "memory_limit: " . ini_get('memory_limit') . "<br>";

// List existing images
echo "<br><h3>Existing Images:</h3>";
if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && $file != 'index.html') {
            echo "- " . $file . " (Size: " . filesize($upload_dir . $file) . " bytes)<br>";
        }
    }
} else {
    echo "Directory does not exist!<br>";
}

// Test form
echo "<br><h3>Test Upload Form:</h3>";
?>

<form action="test_image_upload.php" method="post" enctype="multipart/form-data">
    <p>Select image to upload:</p>
    <input type="file" name="test_image" accept="image/*">
    <br><br>
    <input type="submit" value="Upload Image" name="submit">
</form>

<?php
// Handle test upload
if (isset($_POST['submit'])) {
    echo "<br><h3>Upload Test Results:</h3>";
    
    if (isset($_FILES['test_image'])) {
        echo "File info:<br>";
        echo "- Name: " . $_FILES['test_image']['name'] . "<br>";
        echo "- Type: " . $_FILES['test_image']['type'] . "<br>";
        echo "- Size: " . $_FILES['test_image']['size'] . " bytes<br>";
        echo "- Temp file: " . $_FILES['test_image']['tmp_name'] . "<br>";
        echo "- Error: " . $_FILES['test_image']['error'] . "<br>";
        
        if ($_FILES['test_image']['error'] == 0) {
            $target_file = $upload_dir . basename($_FILES['test_image']['name']);
            if (move_uploaded_file($_FILES['test_image']['tmp_name'], $target_file)) {
                echo "<br><strong>SUCCESS:</strong> File uploaded successfully!<br>";
                echo "File saved as: " . $target_file . "<br>";
                
                // Display the uploaded image
                $web_path = str_replace('./', '', $target_file);
                echo "<br>Uploaded image:<br>";
                echo "<img src='" . $web_path . "' style='max-width: 200px; max-height: 200px;'><br>";
            } else {
                echo "<br><strong>ERROR:</strong> Failed to move uploaded file.<br>";
            }
        } else {
            echo "<br><strong>ERROR:</strong> Upload error code " . $_FILES['test_image']['error'] . "<br>";
            
            $upload_errors = array(
                1 => 'File exceeds upload_max_filesize',
                2 => 'File exceeds MAX_FILE_SIZE',
                3 => 'File was only partially uploaded',
                4 => 'No file was uploaded',
                6 => 'Missing a temporary folder',
                7 => 'Failed to write file to disk',
                8 => 'A PHP extension stopped the file upload'
            );
            
            if (isset($upload_errors[$_FILES['test_image']['error']])) {
                echo "Error description: " . $upload_errors[$_FILES['test_image']['error']] . "<br>";
            }
        }
    } else {
        echo "No file uploaded.<br>";
    }
}

// Check .htaccess file
echo "<br><h3>.htaccess Check:</h3>";
$htaccess_path = './.htaccess';
if (file_exists($htaccess_path)) {
    echo ".htaccess file exists<br>";
    $htaccess_content = file_get_contents($htaccess_path);
    if (strpos($htaccess_content, 'RewriteEngine') !== false) {
        echo "URL rewriting is enabled<br>";
    }
} else {
    echo ".htaccess file not found<br>";
}

// Check base URL configuration
echo "<br><h3>URL Configuration:</h3>";
echo "Current script URL: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script filename: " . $_SERVER['SCRIPT_FILENAME'] . "<br>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
form { background: #f5f5f5; padding: 15px; border-radius: 5px; }
</style>
