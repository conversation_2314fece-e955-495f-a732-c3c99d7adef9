<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------
| EMAIL LANGUAGE LINES
| -------------------------------------------------------------------
| The following language lines are used by the Email library.
|
*/

$lang['email_must_be_array'] = 'طريقة التحقق من البريد الإلكتروني يجب أن تمرر مصفوفة.';
$lang['email_invalid_address'] = 'عنوان بريد إلكتروني غير صالح: %s';
$lang['email_attachment_missing'] = 'غير قادر على تحديد موقع المرفق التالي: %s';
$lang['email_attachment_unreadable'] = 'غير قادر على فتح هذا المرفق: %s';
$lang['email_no_from'] = 'لا يمكن إرسال بريد بدون رأس "From".';
$lang['email_no_recipients'] = 'يجب تضمين المستقبلين: To، Cc، أو Bcc';
$lang['email_send_failure_phpmail'] = 'غير قادر على إرسال بريد باستخدام PHP mail(). قد لا يكون خادمك مكوناً لإرسال البريد بهذه الطريقة.';
$lang['email_send_failure_sendmail'] = 'غير قادر على إرسال بريد باستخدام PHP Sendmail. قد لا يكون خادمك مكوناً لإرسال البريد بهذه الطريقة.';
$lang['email_send_failure_smtp'] = 'غير قادر على إرسال بريد باستخدام PHP SMTP. قد لا يكون خادمك مكوناً لإرسال البريد بهذه الطريقة.';
$lang['email_sent'] = 'تم إرسال رسالتك بنجاح باستخدام البروتوكول التالي: %s';
$lang['email_no_socket'] = 'غير قادر على فتح socket لـ Sendmail. يرجى التحقق من الإعدادات.';
$lang['email_no_hostname'] = 'لم تحدد اسم مضيف SMTP.';
$lang['email_smtp_error'] = 'واجهت خطأ SMTP التالي: %s';
$lang['email_no_smtp_unpw'] = 'خطأ: يجب تعيين اسم مستخدم وكلمة مرور SMTP.';
$lang['email_failed_smtp_login'] = 'فشل في إرسال أمر AUTH LOGIN. خطأ: %s';
$lang['email_smtp_auth_un'] = 'فشل في مصادقة اسم المستخدم. خطأ: %s';
$lang['email_smtp_auth_pw'] = 'فشل في مصادقة كلمة المرور. خطأ: %s';
$lang['email_smtp_data_failure'] = 'غير قادر على إرسال البيانات: %s';
$lang['email_exit_status'] = 'رمز حالة الخروج: %s';
