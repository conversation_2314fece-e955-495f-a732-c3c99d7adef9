<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="display-4"><?php echo $this->lang->line('nav_contact'); ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent">
                        <li class="breadcrumb-item">
                            <a href="<?php echo $is_english ? base_url('en') : base_url(); ?>" class="text-white">
                                <?php echo $this->lang->line('nav_home'); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            <?php echo $this->lang->line('nav_contact'); ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Contact Content -->
<section class="contact-content py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title"><?php echo $this->lang->line('contact_us'); ?></h2>
                <p class="lead">
                    <?php if ($is_arabic): ?>
                        نحن هنا لخدمتك. تواصل معنا لأي استفسار أو طلب خدمة
                    <?php else: ?>
                        We are here to serve you. Contact us for any inquiry or service request
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-5">
                <div class="contact-form-card">
                    <h3 class="mb-4"><?php echo $this->lang->line('contact_form'); ?></h3>

                    <!-- Success/Error Messages -->
                    <?php if ($this->session->flashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $this->session->flashdata('success'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error) && $error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Temporary Debug Info -->
                    <?php if ($_POST): ?>
                        <div class="alert alert-info">
                            <strong>🔍 Debug: Form was submitted!</strong><br>
                            Form data received: <?php echo count($_POST); ?> fields<br>
                            <?php if (validation_errors()): ?>
                                <strong>Validation Errors:</strong><br>
                                <?php echo validation_errors(); ?>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php echo form_open('', array('class' => 'contact-form', 'id' => 'contactForm')); ?>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label"><?php echo $this->lang->line('your_name'); ?> *</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="<?php echo set_value('name'); ?>" required>
                                <?php echo form_error('name', '<div class="text-danger">', '</div>'); ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label"><?php echo $this->lang->line('your_email'); ?> *</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo set_value('email'); ?>" required>
                                <?php echo form_error('email', '<div class="text-danger">', '</div>'); ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label"><?php echo $this->lang->line('your_phone'); ?> *</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="<?php echo set_value('phone'); ?>" required>
                                <?php echo form_error('phone', '<div class="text-danger">', '</div>'); ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label"><?php echo $this->lang->line('subject'); ?> *</label>
                                <input type="text" class="form-control" id="subject" name="subject"
                                       value="<?php echo set_value('subject'); ?>" required>
                                <?php echo form_error('subject', '<div class="text-danger">', '</div>'); ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label"><?php echo $this->lang->line('message'); ?> *</label>
                            <textarea class="form-control" id="message" name="message" rows="5"
                                      required><?php echo set_value('message'); ?></textarea>
                            <?php echo form_error('message', '<div class="text-danger">', '</div>'); ?>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i> <?php echo $this->lang->line('send_message'); ?>
                        </button>
                    <?php echo form_close(); ?>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="contact-info">
                    <h3 class="mb-4"><?php echo $this->lang->line('contact_info'); ?></h3>

                    <div class="contact-item mb-4">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt text-primary"></i>
                        </div>
                        <div class="contact-details">
                            <h5><?php echo $this->lang->line('address'); ?></h5>
                            <p><?php echo $contact_info['address']; ?></p>
                        </div>
                    </div>

                    <div class="contact-item mb-4">
                        <div class="contact-icon">
                            <i class="fas fa-phone text-primary"></i>
                        </div>
                        <div class="contact-details">
                            <h5><?php echo $this->lang->line('phone'); ?></h5>
                            <p>
                                <a href="tel:<?php echo $contact_info['phone']; ?>" class="text-decoration-none">
                                    <?php echo $contact_info['phone']; ?>
                                </a>
                            </p>
                        </div>
                    </div>

                    <div class="contact-item mb-4">
                        <div class="contact-icon">
                            <i class="fas fa-mobile-alt text-primary"></i>
                        </div>
                        <div class="contact-details">
                            <h5><?php echo $this->lang->line('mobile'); ?></h5>
                            <p>
                                <a href="tel:<?php echo $contact_info['mobile']; ?>" class="text-decoration-none">
                                    <?php echo $contact_info['mobile']; ?>
                                </a>
                            </p>
                        </div>
                    </div>

                    <div class="contact-item mb-4">
                        <div class="contact-icon">
                            <i class="fas fa-envelope text-primary"></i>
                        </div>
                        <div class="contact-details">
                            <h5><?php echo $this->lang->line('email'); ?></h5>
                            <p>
                                <a href="mailto:<?php echo $contact_info['email']; ?>" class="text-decoration-none">
                                    <?php echo $contact_info['email']; ?>
                                </a>
                            </p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock text-primary"></i>
                        </div>
                        <div class="contact-details">
                            <h5>
                                <?php if ($is_arabic): ?>
                                    ساعات العمل
                                <?php else: ?>
                                    Working Hours
                                <?php endif; ?>
                            </h5>
                            <p>
                                <?php if ($is_arabic): ?>
                                    السبت - الخميس: 8:00 ص - 6:00 م<br>
                                    الجمعة: مغلق
                                <?php else: ?>
                                    Saturday - Thursday: 8:00 AM - 6:00 PM<br>
                                    Friday: Closed
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="text-center mb-4">
                    <?php if ($is_arabic): ?>
                        موقعنا على الخريطة
                    <?php else: ?>
                        Find Us on the Map
                    <?php endif; ?>
                </h3>
                <div class="map-container">
                    <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3623.9!2d46.6753!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2f03890d489399%3A0xba974d1c98e79fd5!2sRiyadh%20Saudi%20Arabia!5e0!3m2!1sen!2ssa!4v1703123456789!5m2!1sen!2ssa"
                        width="100%"
                        height="400"
                        style="border:0;"
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"
                        class="rounded shadow">
                    </iframe>

                    <!-- Direct Google Maps Link -->
                    <div class="text-center mt-3">
                        <a href="https://maps.app.goo.gl/M49FssELRBD15gH29"
                           target="_blank"
                           class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt me-2"></i>
                            <?php echo $is_arabic ? 'فتح في خرائط جوجل' : 'Open in Google Maps'; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.contact-form-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.contact-icon {
    margin-right: 1rem;
    margin-left: 0;
    flex-shrink: 0;
}

.rtl .contact-icon {
    margin-right: 0;
    margin-left: 1rem;
}

.contact-icon i {
    font-size: 1.5rem;
}

.contact-details h5 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.contact-details p {
    margin-bottom: 0;
    color: #666;
}

.map-container {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}
</style>
