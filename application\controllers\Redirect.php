<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Redirect extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
    }

    public function arabic_home()
    {
        // Redirect URL-encoded Arabic home page to proper home
        redirect(base_url());
    }
    
    public function handle_encoded_arabic($encoded_path = '')
    {
        // Decode the Arabic path
        $decoded_path = urldecode($encoded_path);
        
        // Map common Arabic routes
        $arabic_routes = array(
            'الرئيسية' => '',
            'العروض-العقارية' => 'العروض-العقارية',
            'من-نحن' => 'من-نحن',
            'اتصل-بنا' => 'اتصل-بنا',
            'الموظفين' => 'الموظفين'
        );
        
        // Check if we have a mapping for this path
        if (array_key_exists($decoded_path, $arabic_routes)) {
            $redirect_path = $arabic_routes[$decoded_path];
            if (empty($redirect_path)) {
                redirect(base_url());
            } else {
                redirect(base_url($redirect_path));
            }
        } else {
            // If no mapping found, redirect to home
            redirect(base_url());
        }
    }
}
