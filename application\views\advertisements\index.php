<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="display-4"><?php echo $this->lang->line('nav_advertisements'); ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent">
                        <li class="breadcrumb-item">
                            <a href="<?php echo $is_english ? base_url('en') : base_url(); ?>" class="text-white">
                                <?php echo $this->lang->line('nav_home'); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            <?php echo $this->lang->line('nav_advertisements'); ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Search Filters -->
<section class="search-filters py-4 bg-light">
    <div class="container">
        <div class="filter-card">
            <h5 class="mb-3">
                <?php if ($is_arabic): ?>
                    تصفية العقارات
                <?php else: ?>
                    Filter Properties
                <?php endif; ?>
            </h5>
            <form action="<?php echo current_url(); ?>" method="GET" class="filter-form">
                <div class="row g-3">
                    <div class="col-md-3">
                        <select name="type" class="form-select">
                            <option value=""><?php echo $this->lang->line('all'); ?> <?php echo $this->lang->line('property_type'); ?></option>
                            <?php if (!empty($property_types)): ?>
                                <?php foreach ($property_types as $type): ?>
                                    <option value="<?php echo $type->type; ?>"
                                            <?php echo (isset($filters['type']) && $filters['type'] == $type->type) ? 'selected' : ''; ?>>
                                        <?php
                                        if ($is_arabic) {
                                            echo ($type->type == 'residential') ? 'سكني' :
                                                 (($type->type == 'commercial') ? 'تجاري' : 'إداري');
                                        } else {
                                            echo ucfirst($type->type);
                                        }
                                        ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <select name="location" class="form-select">
                            <option value=""><?php echo $this->lang->line('all'); ?> <?php echo $this->lang->line('property_location'); ?></option>
                            <?php if (!empty($locations)): ?>
                                <?php foreach ($locations as $location): ?>
                                    <option value="<?php echo $is_arabic ? $location->location_ar : $location->location_en; ?>"
                                            <?php echo (isset($filters['location']) && $filters['location'] == ($is_arabic ? $location->location_ar : $location->location_en)) ? 'selected' : ''; ?>>
                                        <?php echo $is_arabic ? $location->location_ar : $location->location_en; ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <input type="number" name="min_price" class="form-control"
                               placeholder="<?php echo $is_arabic ? 'أقل سعر' : 'Min Price'; ?>"
                               value="<?php echo isset($filters['min_price']) ? $filters['min_price'] : ''; ?>">
                    </div>

                    <div class="col-md-2">
                        <input type="number" name="max_price" class="form-control"
                               placeholder="<?php echo $is_arabic ? 'أعلى سعر' : 'Max Price'; ?>"
                               value="<?php echo isset($filters['max_price']) ? $filters['max_price'] : ''; ?>">
                    </div>

                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> <?php echo $this->lang->line('search'); ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Properties Listing -->
<section class="properties-listing py-5">
    <div class="container">
        <?php if (!empty($properties)): ?>
            <div class="row g-4">
                <?php foreach ($properties as $property): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="property-card h-100">
                            <div class="property-image">
                                <?php
                                // Fix: Correct paths for alyanabea_feb24 project
                                $file_system_path = './assets/images/properties/';  // For file_exists() - current directory
                                $web_url_path = 'assets/images/properties/';        // For base_url() - relative to base URL
                                ?>
                                <?php if (!empty($property->image) && file_exists($file_system_path . $property->image)): ?>
                                    <img src="<?php echo base_url($web_url_path . $property->image); ?>"
                                         alt="<?php echo $is_arabic ? $property->title_ar : $property->title_en; ?>"
                                         class="img-fluid">
                                <?php else: ?>
                                    <div class="property-placeholder"
                                         style="height: 250px; background: url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80') center/cover; position: relative;">
                                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(9, 60, 60, 0.7); display: flex; align-items: center; justify-content: center; color: white;">
                                            <div class="text-center">
                                                <i class="fas fa-home fa-3x mb-2 opacity-75"></i>
                                                <p class="mb-0"><?php echo $is_arabic ? 'صورة العقار' : 'Property Image'; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="property-badges">
                                    <span class="badge bg-primary">
                                        <?php
                                        if ($is_arabic) {
                                            echo ($property->type == 'residential') ? 'سكني' :
                                                 (($property->type == 'commercial') ? 'تجاري' : 'إداري');
                                        } else {
                                            echo ucfirst($property->type);
                                        }
                                        ?>
                                    </span>
                                    <?php if ($property->featured): ?>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $is_arabic ? 'مميز' : 'Featured'; ?>
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Gallery Indicator -->
                                <?php
                                $gallery_images = !empty($property->gallery) ? json_decode($property->gallery, true) : array();
                                $has_main_image = !empty($property->image) && file_exists($file_system_path . $property->image);
                                $total_images = ($has_main_image ? 1 : 0) + count($gallery_images);
                                ?>
                                <?php if ($total_images > 1): ?>
                                    <div class="gallery-indicator position-absolute bottom-0 start-0 m-3">
                                        <span class="badge bg-dark bg-opacity-75">
                                            <i class="fas fa-images me-1"></i>
                                            <?php echo $total_images; ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="property-content p-4">
                                <h5 class="property-title mb-2">
                                    <?php echo character_limiter($is_arabic ? $property->title_ar : $property->title_en, 50); ?>
                                </h5>
                                <p class="property-location text-muted mb-2">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo $is_arabic ? $property->location_ar : $property->location_en; ?>
                                </p>
                                <p class="property-description text-muted mb-3">
                                    <?php echo character_limiter($is_arabic ? $property->description_ar : $property->description_en, 100); ?>
                                </p>

                                <?php if ($property->area || $property->bedrooms || $property->bathrooms): ?>
                                    <div class="property-features mb-3">
                                        <small class="text-muted">
                                            <?php if ($property->area): ?>
                                                <i class="fas fa-ruler-combined"></i> <?php echo $property->area; ?> <?php echo $is_arabic ? 'م²' : 'm²'; ?>
                                            <?php endif; ?>

                                            <?php if ($property->bedrooms): ?>
                                                <i class="fas fa-bed ms-2"></i> <?php echo $property->bedrooms; ?> <?php echo $is_arabic ? 'غرف' : 'Beds'; ?>
                                            <?php endif; ?>

                                            <?php if ($property->bathrooms): ?>
                                                <i class="fas fa-bath ms-2"></i> <?php echo $property->bathrooms; ?> <?php echo $is_arabic ? 'حمامات' : 'Baths'; ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                <?php endif; ?>

                                <div class="property-footer d-flex justify-content-between align-items-center">
                                    <span class="property-price h5 text-primary mb-0">
                                        <?php echo number_format($property->price); ?> <?php echo $is_arabic ? 'ريال' : 'SAR'; ?>
                                    </span>
                                    <a href="<?php echo base_url('advertisements/' . $property->id); ?>"
                                       class="btn btn-outline-primary btn-sm">
                                        <?php echo $this->lang->line('view_details'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if (!empty($pagination)): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <?php echo $pagination; ?>
                    </div>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="no-properties text-center py-5">
                        <i class="fas fa-home fa-5x text-muted mb-4"></i>
                        <h3><?php echo $this->lang->line('no_properties_found'); ?></h3>
                        <p class="lead text-muted">
                            <?php if ($is_arabic): ?>
                                عذراً، لا توجد عقارات تطابق معايير البحث الخاصة بك.
                            <?php else: ?>
                                Sorry, no properties match your search criteria.
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo $is_english ? base_url('en/advertisements') : base_url('العروض-العقارية'); ?>"
                           class="btn btn-primary">
                            <i class="fas fa-refresh"></i>
                            <?php if ($is_arabic): ?>
                                عرض جميع العقارات
                            <?php else: ?>
                                View All Properties
                            <?php endif; ?>
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<style>
.filter-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.property-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.property-card:hover .property-image img {
    transform: scale(1.1);
}

.property-badges {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 2;
}

.rtl .property-badges {
    right: auto;
    left: 1rem;
}

.property-badges .badge {
    margin-left: 0.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.rtl .property-badges .badge {
    margin-left: 0;
    margin-right: 0.5rem;
}

.no-properties {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
</style>
