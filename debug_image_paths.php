<?php
// Debug Image Paths Script
echo "<h1>🔍 Image Path Debugging</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

echo "<h2>1. Server Environment</h2>";
echo "Current working directory: <strong>" . getcwd() . "</strong><br>";
echo "Document root: <strong>" . $_SERVER['DOCUMENT_ROOT'] . "</strong><br>";
echo "Script filename: <strong>" . $_SERVER['SCRIPT_FILENAME'] . "</strong><br>";
echo "Script directory: <strong>" . dirname($_SERVER['SCRIPT_FILENAME']) . "</strong><br>";

echo "<h2>2. Directory Structure Check</h2>";

// Test different path approaches
$path_tests = [
    'Relative from current' => './assets/images/properties/',
    'Relative without dot' => 'assets/images/properties/',
    'Absolute from doc root' => $_SERVER['DOCUMENT_ROOT'] . '/assets/images/properties/',
    'Absolute with project' => $_SERVER['DOCUMENT_ROOT'] . '/alyanabea_feb24/assets/images/properties/',
    'From script dir' => dirname($_SERVER['SCRIPT_FILENAME']) . '/assets/images/properties/'
];

foreach ($path_tests as $label => $path) {
    echo "<strong>$label:</strong><br>";
    echo "&nbsp;&nbsp;Path: <code>$path</code><br>";
    echo "&nbsp;&nbsp;Exists: " . (is_dir($path) ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "<br>";
    echo "&nbsp;&nbsp;Readable: " . (is_readable($path) ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "<br>";
    
    if (is_dir($path)) {
        $files = array_diff(scandir($path), array('.', '..', 'index.html'));
        echo "&nbsp;&nbsp;Files found: <span class='info'>" . count($files) . "</span><br>";
        if (count($files) > 0) {
            echo "&nbsp;&nbsp;Sample files: ";
            $sample_files = array_slice($files, 0, 3);
            foreach ($sample_files as $file) {
                echo "<code>$file</code> ";
            }
            echo "<br>";
        }
    }
    echo "<br>";
}

echo "<h2>3. Test with Actual Property Images</h2>";

// Try to connect to database to get actual property data
try {
    // Simple database connection test
    if (file_exists('application/config/database.php')) {
        include 'application/config/database.php';
        
        if (isset($db['default'])) {
            $host = $db['default']['hostname'];
            $username = $db['default']['username'];
            $password = $db['default']['password'];
            $database = $db['default']['database'];
            
            echo "Attempting database connection...<br>";
            
            $connection = new mysqli($host, $username, $password, $database);
            
            if ($connection->connect_error) {
                echo "<span class='error'>❌ Database connection failed: " . $connection->connect_error . "</span><br>";
            } else {
                echo "<span class='success'>✅ Database connected successfully</span><br>";
                
                // Get some property images
                $query = "SELECT id, title_en, image FROM properties WHERE image IS NOT NULL AND image != '' LIMIT 5";
                $result = $connection->query($query);
                
                if ($result && $result->num_rows > 0) {
                    echo "<h3>Testing with actual property images:</h3>";
                    
                    while ($row = $result->fetch_assoc()) {
                        echo "<div style='border:1px solid #ccc; padding:10px; margin:10px 0;'>";
                        echo "<strong>Property ID: " . $row['id'] . "</strong><br>";
                        echo "Title: " . $row['title_en'] . "<br>";
                        echo "Image filename: <code>" . $row['image'] . "</code><br>";
                        
                        // Test each path with this actual image
                        foreach ($path_tests as $label => $path) {
                            if (is_dir($path)) {
                                $full_image_path = $path . $row['image'];
                                $exists = file_exists($full_image_path);
                                echo "&nbsp;&nbsp;$label: " . ($exists ? "<span class='success'>✅ FOUND</span>" : "<span class='error'>❌ NOT FOUND</span>") . "<br>";
                                
                                if ($exists) {
                                    $file_size = filesize($full_image_path);
                                    echo "&nbsp;&nbsp;&nbsp;&nbsp;Size: " . number_format($file_size) . " bytes<br>";
                                }
                            }
                        }
                        echo "</div>";
                    }
                } else {
                    echo "<span class='error'>❌ No properties with images found in database</span><br>";
                }
                
                $connection->close();
            }
        }
    } else {
        echo "<span class='error'>❌ Database config file not found</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Error: " . $e->getMessage() . "</span><br>";
}

echo "<h2>4. Manual File Check</h2>";

// Let's manually check what's in the properties directory
$possible_dirs = [
    './assets/images/properties/',
    'assets/images/properties/',
    $_SERVER['DOCUMENT_ROOT'] . '/alyanabea_feb24/assets/images/properties/'
];

foreach ($possible_dirs as $dir) {
    if (is_dir($dir)) {
        echo "<h3>Contents of: <code>$dir</code></h3>";
        $files = array_diff(scandir($dir), array('.', '..'));
        
        if (count($files) > 0) {
            echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
            echo "<tr><th>Filename</th><th>Size</th><th>Type</th><th>Permissions</th></tr>";
            
            foreach ($files as $file) {
                $file_path = $dir . $file;
                $file_size = filesize($file_path);
                $file_type = is_file($file_path) ? mime_content_type($file_path) : 'directory';
                $permissions = substr(sprintf('%o', fileperms($file_path)), -4);
                
                echo "<tr>";
                echo "<td><code>$file</code></td>";
                echo "<td>" . number_format($file_size) . " bytes</td>";
                echo "<td>$file_type</td>";
                echo "<td>$permissions</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<span class='error'>❌ Directory is empty</span><br>";
        }
        break; // Only show the first directory that exists
    }
}

echo "<h2>5. Recommended Solution</h2>";
echo "<div style='background:#e8f5e8; padding:15px; border-left:4px solid #4CAF50;'>";
echo "<strong>Based on the tests above, use the path that shows ✅ FOUND for your images.</strong><br><br>";
echo "Update your view files to use the working path:<br>";
echo "<code>\$image_base_path = 'THE_WORKING_PATH_FROM_ABOVE';</code><br><br>";
echo "Common working paths:<br>";
echo "• <code>./assets/images/properties/</code> (most common)<br>";
echo "• <code>assets/images/properties/</code> (alternative)<br>";
echo "• Full absolute path if relative paths don't work<br>";
echo "</div>";

echo "<h2>6. Quick Test</h2>";
echo "<p>If you found working paths above, test them here:</p>";

if ($_POST['test_path'] ?? false) {
    $test_path = $_POST['test_path'];
    echo "<h3>Testing path: <code>$test_path</code></h3>";
    
    if (is_dir($test_path)) {
        echo "<span class='success'>✅ Directory exists and is accessible</span><br>";
        $files = array_diff(scandir($test_path), array('.', '..', 'index.html'));
        echo "Files found: " . count($files) . "<br>";
        
        if (count($files) > 0) {
            $test_file = array_values($files)[0];
            $test_file_path = $test_path . $test_file;
            echo "Testing file: <code>$test_file</code><br>";
            echo "file_exists() result: " . (file_exists($test_file_path) ? "<span class='success'>✅ TRUE</span>" : "<span class='error'>❌ FALSE</span>") . "<br>";
        }
    } else {
        echo "<span class='error'>❌ Directory does not exist or is not accessible</span><br>";
    }
}
?>

<form method="post" style="background:#f0f0f0; padding:15px; border-radius:5px;">
    <label>Test a path:</label><br>
    <input type="text" name="test_path" value="./assets/images/properties/" style="width:300px; padding:5px;">
    <input type="submit" value="Test Path" style="background:#007bff; color:white; padding:5px 15px; border:none;">
</form>

<p><strong>Next step:</strong> Once you identify the working path, update your view files to use that exact path.</p>
