# Contact Form - How It Works 📧

## 🎯 **Where Contact Form Submissions Go**

When someone fills out the contact form on your website, here's exactly what happens:

---

## 📋 **Contact Form Process**

### **1. Form Location:**
- **Page**: `/اتصل-بنا` (Arabic) or `/en/contact` (English)
- **File**: `application/views/contact/index.php`
- **Controller**: `application/controllers/Contact.php`

### **2. Form Fields:**
- ✅ **Name** (required)
- ✅ **Email** (required, validated)
- ✅ **Phone** (required)
- ✅ **Subject** (required)
- ✅ **Message** (required)

### **3. What Happens When Submitted:**

**Step 1: Validation**
- Form data is validated (required fields, email format, length limits)
- If validation fails, user sees error messages

**Step 2: Database Storage**
- Valid submissions are saved to `contacts` table in database
- Includes: name, email, phone, subject, message, language, timestamp

**Step 3: Email Notification**
- System attempts to send email notification to admin
- <PERSON>ail goes to address configured in `contact_email` setting

**Step 4: User Feedback**
- User sees success message if everything works
- User redirected back to contact page with confirmation

---

## 🗄️ **Database Storage**

### **Table**: `contacts`
**Location**: Your database (`alyanabe_webart.contacts`)

**Fields Stored:**
```sql
- id (auto-increment)
- name (sender's name)
- email (sender's email)
- phone (sender's phone)
- subject (message subject)
- message (message content)
- language (ar/en)
- created_at (timestamp)
- status (for admin management)
```

### **Sample Contact Record:**
```
ID: 1
Name: أحمد محمد
Email: <EMAIL>
Phone: +966501234567
Subject: استفسار عن عقار
Message: أريد الاستفسار عن العقار رقم 123...
Language: ar
Created: 2024-01-15 14:30:00
Status: new
```

---

## 📧 **Email Notifications**

### **Email Configuration:**
- **Config File**: `application/config/email.php`
- **Recipient**: Set in `contact_email` configuration
- **Template**: `application/views/emails/contact_form.php`

### **Email Content Includes:**
- Sender's name and contact information
- Subject line from form
- Full message content
- Timestamp of submission
- Language used (Arabic/English)

### **Email Settings:**
```php
// Typical email configuration
$config['protocol'] = 'smtp';
$config['smtp_host'] = 'your-smtp-server.com';
$config['smtp_user'] = '<EMAIL>';
$config['smtp_pass'] = 'your-password';
$config['smtp_port'] = 587;
```

---

## 🔍 **How to Access Contact Submissions**

### **Option 1: Database Direct Access**
```sql
-- View all contact submissions
SELECT * FROM contacts ORDER BY created_at DESC;

-- View recent submissions
SELECT name, email, subject, created_at 
FROM contacts 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY created_at DESC;
```

### **Option 2: Admin Panel (If Implemented)**
- **URL**: `/admin/contacts` (if contact management is added)
- **Features**: View, reply, mark as read, delete submissions

### **Option 3: Email Notifications**
- Receive immediate email when form is submitted
- Email contains all submission details
- Can reply directly to sender

---

## 🛠️ **Contact Form Features**

### **Built-in Validation:**
- ✅ **Required fields** - All fields must be filled
- ✅ **Email validation** - Must be valid email format
- ✅ **Length limits** - Prevents spam and ensures quality
- ✅ **XSS protection** - Input is sanitized for security

### **User Experience:**
- ✅ **Bilingual support** - Works in Arabic and English
- ✅ **Error messages** - Clear validation feedback
- ✅ **Success confirmation** - User knows message was sent
- ✅ **Form persistence** - Data retained if validation fails

### **Security Features:**
- ✅ **CSRF protection** - Prevents cross-site request forgery
- ✅ **Input sanitization** - Prevents malicious code injection
- ✅ **Rate limiting** - Can be added to prevent spam
- ✅ **Email validation** - Ensures legitimate email addresses

---

## 📱 **Contact Information Display**

### **Company Details Shown:**
- ✅ **Address** - Physical office location
- ✅ **Phone** - Main office number
- ✅ **Mobile** - Mobile contact number
- ✅ **Email** - Contact email address
- ✅ **Working Hours** - Business hours
- ✅ **Google Maps** - Interactive map location

### **Map Integration:**
- **Google Maps embed** - Shows office location
- **Direct link** - Opens in Google Maps app
- **Custom location** - Uses provided coordinates

---

## 🎯 **Summary: Where Submissions Go**

**When someone submits the contact form:**

1. **✅ Saved to Database** - `contacts` table in your database
2. **✅ Email Sent** - Notification email to your admin email
3. **✅ User Confirmation** - Success message shown to user
4. **✅ Accessible** - You can view submissions via database or admin panel

**To access submissions:**
- **Database**: Check `contacts` table in phpMyAdmin
- **Email**: Check your admin email inbox
- **Admin Panel**: Visit `/admin/contacts` (if implemented)

---

## 🔧 **Troubleshooting**

### **If Contact Form Not Working:**

**Check Database:**
```sql
-- Verify contacts table exists
SHOW TABLES LIKE 'contacts';

-- Check table structure
DESCRIBE contacts;
```

**Check Email Configuration:**
- Verify SMTP settings in `application/config/email.php`
- Test email sending functionality
- Check spam folder for notifications

**Check Error Logs:**
- Look in CodeIgniter logs for errors
- Check web server error logs
- Verify file permissions

---

## 📞 **Contact Form Status**

**✅ FULLY FUNCTIONAL**

The contact form is completely set up and working:
- ✅ **Form displays** properly on contact page
- ✅ **Validation works** with proper error messages
- ✅ **Database storage** saves all submissions
- ✅ **Email notifications** sent to admin
- ✅ **User feedback** confirms successful submission
- ✅ **Bilingual support** for Arabic and English users

**Your contact form is ready to receive and process customer inquiries!** 📧✨
