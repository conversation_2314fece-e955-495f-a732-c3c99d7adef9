-- Create Contacts Table for Contact Form Submissions
-- Run this SQL in your database to fix the contact form issue

USE alyanabe_webart;

-- <PERSON>reate contacts table
CREATE TABLE IF NOT EXISTS `contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'Sender name',
  `email` varchar(100) NOT NULL COMMENT 'Sender email',
  `phone` varchar(20) NOT NULL COMMENT 'Sender phone',
  `subject` varchar(200) NOT NULL COMMENT 'Message subject',
  `message` text NOT NULL COMMENT 'Message content',
  `language` varchar(2) DEFAULT 'ar' COMMENT 'Language used (ar/en)',
  `status` enum('new','read','replied','archived') DEFAULT 'new' COMMENT 'Message status',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'Sender IP address',
  `user_agent` text DEFAULT NULL COMMENT 'Browser information',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_language` (`language`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Contact form submissions';

-- Insert a test record to verify table works
INSERT INTO `contacts` (`name`, `email`, `phone`, `subject`, `message`, `language`, `status`) VALUES
('Test User', '<EMAIL>', '+966501234567', 'Test Message', 'This is a test message to verify the contacts table is working properly.', 'en', 'new');

-- Success message
SELECT 'Contacts table created successfully!' as message;
SELECT COUNT(*) as total_contacts FROM contacts;
