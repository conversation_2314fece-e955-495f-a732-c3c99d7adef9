-- Create Staff Table for Alyanabea Website
-- Run this SQL in your database to fix the staff table error

USE alyanabe_webart;

-- Create staff table with bilingual support
CREATE TABLE IF NOT EXISTS `staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name_ar` varchar(100) NOT NULL COMMENT 'Arabic name',
  `name_en` varchar(100) NOT NULL COMMENT 'English name',
  `position_ar` varchar(100) NOT NULL COMMENT 'Arabic position',
  `position_en` varchar(100) NOT NULL COMMENT 'English position',
  `bio_ar` text COMMENT 'Arabic biography',
  `bio_en` text COMMENT 'English biography',
  `image` varchar(255) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `experience_years` int(11) DEFAULT NULL,
  `specialties_ar` text COMMENT 'Comma-separated specialties in Arabic',
  `specialties_en` text COMMENT 'Comma-separated specialties in English',
  `featured` tinyint(1) DEFAULT 0,
  `display_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`featured`),
  KEY `idx_display_order` (`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample staff data
INSERT INTO `staff` (`name_ar`, `name_en`, `position_ar`, `position_en`, `bio_ar`, `bio_en`, `experience_years`, `specialties_ar`, `specialties_en`, `featured`, `display_order`, `status`) VALUES
('أحمد محمد العلي', 'Ahmed Mohammed Al-Ali', 'مدير المكتب', 'Office Manager', 'مدير مكتب ألوان الينابيع للعقارات مع خبرة تزيد عن 15 عاماً في مجال العقارات وإدارة الأملاك', 'Office Manager at Alwan Al-Yanabea Real Estate with over 15 years of experience in real estate and property management', 15, 'إدارة الأملاك,التسويق العقاري,خدمة العملاء', 'Property Management,Real Estate Marketing,Customer Service', 1, 1, 'active'),

('فاطمة أحمد السالم', 'Fatima Ahmed Al-Salem', 'مستشارة عقارية', 'Real Estate Consultant', 'مستشارة عقارية متخصصة في العقارات السكنية والتجارية مع خبرة 10 سنوات', 'Real Estate Consultant specializing in residential and commercial properties with 10 years of experience', 10, 'العقارات السكنية,العقارات التجارية,الاستشارات العقارية', 'Residential Properties,Commercial Properties,Real Estate Consulting', 1, 2, 'active'),

('محمد عبدالله الخالد', 'Mohammed Abdullah Al-Khalid', 'مسوق عقاري', 'Real Estate Marketer', 'خبير في التسويق العقاري والترويج للعقارات عبر القنوات المختلفة', 'Expert in real estate marketing and property promotion through various channels', 8, 'التسويق الرقمي,التصوير العقاري,إدارة المواقع الإلكترونية', 'Digital Marketing,Property Photography,Website Management', 0, 3, 'active'),

('سارة محمد الأحمد', 'Sarah Mohammed Al-Ahmed', 'منسقة خدمة العملاء', 'Customer Service Coordinator', 'منسقة خدمة العملاء المسؤولة عن متابعة العملاء وتقديم الدعم اللازم', 'Customer Service Coordinator responsible for client follow-up and providing necessary support', 5, 'خدمة العملاء,المتابعة,التنسيق', 'Customer Service,Follow-up,Coordination', 0, 4, 'active');

-- Create indexes for better performance
CREATE INDEX idx_staff_display ON staff(display_order, featured, status);

-- Success message
SELECT 'Staff table created successfully!' as message;
