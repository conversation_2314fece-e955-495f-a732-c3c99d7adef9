# Alwan Al-Yanabea Real Estate Website

A bilingual (Arabic/English) real estate website built with CodeIgniter 3, featuring RTL/LTR support and a modern design using dark green (#093c3c) and white color scheme.

## 🌟 Features

### 🌐 Bilingual Support
- **Arabic (RTL)** - Default language with right-to-left layout
- **English (LTR)** - Secondary language with left-to-right layout
- Dynamic language switching with URL-based routing
- Separate content management for both languages

### 🏠 Real Estate Functionality
- Property listings with advanced filtering
- Property detail pages with image galleries
- Staff/Team member profiles
- Contact form with validation
- About us page with company information
- Responsive design for all devices

### 🎨 Design & UI
- **Color Scheme**: Dark Green (#093c3c) and White
- Modern, clean interface
- Bootstrap 5 framework
- Custom CSS with animations
- Mobile-first responsive design
- RTL/LTR layout support

### 🔧 Technical Features
- CodeIgniter 3 MVC architecture
- MySQL database with UTF-8 support
- SEO-friendly URLs
- Form validation (client & server-side)
- Security measures and input sanitization
- Performance optimizations

## 📁 Project Structure

```
alyanabea-website/
├── application/
│   ├── config/
│   │   ├── autoload.php
│   │   ├── config.php
│   │   ├── database.php
│   │   └── routes.php
│   ├── controllers/
│   │   ├── Home.php
│   │   ├── About.php
│   │   ├── Contact.php
│   │   ├── Advertisements.php
│   │   ├── Staff.php
│   │   └── Language.php
│   ├── core/
│   │   └── MY_Controller.php
│   ├── models/
│   │   ├── Property_model.php
│   │   ├── Contact_model.php
│   │   └── Staff_model.php
│   ├── views/
│   │   ├── templates/
│   │   │   ├── header.php
│   │   │   └── footer.php
│   │   ├── home/
│   │   ├── about/
│   │   ├── contact/
│   │   ├── advertisements/
│   │   └── staff/
│   └── language/
│       ├── arabic/
│       │   └── main_lang.php
│       └── english/
│           └── main_lang.php
├── assets/
│   ├── css/
│   │   ├── style.css
│   │   └── rtl.css
│   ├── js/
│   │   └── main.js
│   └── images/
├── database/
│   └── alyanabea_db.sql
├── index.php
├── .htaccess
└── README.md
```

## 🚀 Installation

### Prerequisites
- PHP 7.0 or higher
- MySQL 5.6 or higher
- Apache/Nginx web server
- mod_rewrite enabled

### Setup Steps

1. **Download CodeIgniter 3**
   ```bash
   # Download CI3 from https://codeigniter.com/download
   # Extract and copy the 'system' folder to your project directory
   ```

2. **Database Setup**
   ```sql
   -- Create database
   CREATE DATABASE alyanabea_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   
   -- Import the SQL file
   mysql -u username -p alyanabea_db < database/alyanabea_db.sql
   ```

3. **Configuration**
   
   Update `application/config/database.php`:
   ```php
   $db['default'] = array(
       'hostname' => 'localhost',
       'username' => 'your_username',
       'password' => 'your_password',
       'database' => 'alyanabea_db',
       // ... other settings
   );
   ```
   
   Update `application/config/config.php`:
   ```php
   $config['base_url'] = 'http://localhost/alyanabea-website/';
   ```

4. **File Permissions**
   ```bash
   chmod 755 application/logs/
   chmod 755 assets/images/
   ```

5. **Web Server Setup**
   - Place project in web server directory
   - Ensure mod_rewrite is enabled
   - Configure virtual host (optional)

## 🌐 URL Structure

### Arabic (Default)
- Homepage: `/`
- Properties: `/العروض-العقارية`
- About: `/من-نحن`
- Contact: `/اتصل-بنا`
- Staff: `/الموظفين`

### English
- Homepage: `/en`
- Properties: `/en/advertisements`
- About: `/en/about`
- Contact: `/en/contact`
- Staff: `/en/staff`

## 🗄️ Database Schema

### Main Tables

1. **properties** - Bilingual property listings
   - Arabic/English titles and descriptions
   - Property type, price, location
   - Features, images, status

2. **contacts** - Contact form submissions
   - Name, email, phone, message
   - Language preference
   - Status tracking

3. **staff** - Team member profiles
   - Arabic/English names and positions
   - Biography, experience, specialties
   - Contact information

4. **property_inquiries** - Property-specific inquiries
5. **property_views** - View tracking

## 🎨 Customization

### Colors
The website uses CSS variables for easy color customization:
```css
:root {
    --primary-color: #093c3c;
    --primary-dark: #072d2d;
    --primary-light: #0b4a4a;
    --secondary-color: #ffffff;
}
```

### Languages
Add new languages by:
1. Creating language files in `application/language/[language]/`
2. Updating `$config['supported_languages']` in config.php
3. Adding routes in routes.php

### Content
- Update language files for text content
- Modify views for layout changes
- Add/edit database content for properties and staff

## 🔒 Security Features

- Input validation and sanitization
- XSS protection
- CSRF protection (configurable)
- SQL injection prevention
- Secure file permissions
- Security headers in .htaccess

## 📱 Responsive Design

- Mobile-first approach
- Bootstrap 5 grid system
- Touch-friendly interface
- Optimized for all screen sizes
- RTL/LTR layout support

## 🌍 SEO Features

- Clean, semantic URLs
- Meta tags and descriptions
- Open Graph tags
- Structured data ready
- Multilingual SEO support

## 🔧 Development

### Adding New Pages
1. Create controller in `application/controllers/`
2. Create model if needed
3. Create views in `application/views/`
4. Add routes in `application/config/routes.php`
5. Update navigation in header template

### Adding New Languages
1. Create language folder and files
2. Update configuration
3. Add routing rules
4. Test all functionality

## 📊 Performance

- Optimized database queries
- Image optimization
- CSS/JS minification ready
- Caching headers
- Gzip compression

## 🐛 Troubleshooting

### Common Issues

1. **Blank Page**
   - Check PHP error logs
   - Verify database connection
   - Ensure system folder exists

2. **Language Not Switching**
   - Check session configuration
   - Verify language files exist
   - Check routing rules

3. **CSS/JS Not Loading**
   - Verify file paths
   - Check .htaccess rules
   - Clear browser cache

## 📞 Support

For support and questions:
- Check the documentation
- Review error logs
- Verify configuration settings

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Credits

- CodeIgniter 3 Framework
- Bootstrap 5
- Font Awesome
- Google Fonts (Noto Sans Arabic, Inter)

---

**Built with ❤️ for Alwan Al-Yanabea Real Estate**
