<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
*/
$route['default_controller'] = 'home';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

// Language routes
$route['lang/(:any)'] = 'language/switch_language/$1';

// Debug routes
$route['debug'] = 'debug/index';
$route['debug/arabic'] = 'debug/test_arabic';
$route['debug/contact'] = 'contact/debug';
$route['debug/en-contact'] = 'contact/debug_english';

// Admin routes
$route['admin'] = 'admin/index';
$route['admin/add_property'] = 'admin/add_property';
$route['admin/edit_property/(:num)'] = 'admin/edit_property/$1';
$route['admin/delete_property/(:num)'] = 'admin/delete_property/$1';
$route['admin/toggle_featured/(:num)'] = 'admin/toggle_featured/$1';

// Handle URL-encoded Arabic routes (fallback)
$route['%D8%A7%D9%84%D8%B1%D8%A6%D9%8A%D8%B3%D9%8A%D8%A9'] = 'home/index';
$route['%D8%A7%D9%84%D8%B9%D8%B1%D9%88%D8%B6-%D8%A7%D9%84%D8%B9%D9%82%D8%A7%D8%B1%D9%8A%D8%A9'] = 'advertisements/index';
$route['%D9%85%D9%86-%D9%86%D8%AD%D9%86'] = 'about/index';
$route['%D8%A7%D8%AA%D8%B5%D9%84-%D8%A8%D9%86%D8%A7'] = 'contact/index';
$route['%D8%A7%D9%84%D9%85%D9%88%D8%B8%D9%81%D9%8A%D9%86'] = 'staff/index';

// Arabic routes (default) - UTF-8
$route['الرئيسية'] = 'home/index';
$route['العروض-العقارية'] = 'advertisements/index';
$route['من-نحن'] = 'about/index';
$route['اتصل-بنا'] = 'contact/index';
$route['الموظفين'] = 'staff/index';

// English routes
$route['en'] = 'home/index';
$route['en/home'] = 'home/index';
$route['en/advertisements'] = 'advertisements/index';
$route['en/about'] = 'about/index';
$route['en/contact'] = 'contact/index';
$route['en/staff'] = 'staff/index';

// Property routes
$route['advertisements/(:num)'] = 'advertisements/view/$1';
$route['العروض-العقارية/(:num)'] = 'advertisements/view/$1';
$route['en/advertisements/(:num)'] = 'advertisements/view/$1';

// Staff routes
$route['staff/(:num)'] = 'staff/view/$1';
$route['الموظفين/(:num)'] = 'staff/view/$1';
$route['en/staff/(:num)'] = 'staff/view/$1';
