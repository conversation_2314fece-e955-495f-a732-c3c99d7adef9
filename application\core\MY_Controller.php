<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MY_Controller extends CI_Controller {

    protected $current_language;
    protected $supported_languages;
    protected $language_data;

    public function __construct()
    {
        parent::__construct();

        // Load language configuration
        $this->supported_languages = $this->config->item('supported_languages');

        // Determine current language
        $this->_set_language();

        // Load language files
        $this->_load_language_files();

        // Set global data for views
        $this->_set_global_data();
    }

    private function _set_language()
    {
        // Check if language is set in session
        $session_lang = $this->session->userdata('language');

        // Check if language is in URL
        $uri_lang = $this->uri->segment(1);
        $current_uri = uri_string();

        // Debug logging
        log_message('debug', 'Language Detection - URI: ' . $current_uri . ', First Segment: ' . $uri_lang . ', Session: ' . $session_lang);

        // Determine language
        if ($uri_lang === 'en') {
            $this->current_language = 'english';
        } elseif (in_array($current_uri, ['الرئيسية', 'العروض-العقارية', 'من-نحن', 'اتصل-بنا', 'الموظفين']) ||
                  preg_match('/^(العروض-العقارية|الموظفين)\/\d+$/', $current_uri)) {
            // Arabic URL detected
            $this->current_language = 'arabic';
        } elseif ($session_lang && array_key_exists($session_lang, $this->supported_languages)) {
            $this->current_language = $session_lang;
        } else {
            $this->current_language = $this->config->item('default_language');
        }

        // Debug logging
        log_message('debug', 'Language Set To: ' . $this->current_language);

        // Set session
        $this->session->set_userdata('language', $this->current_language);

        // Set config language
        $this->config->set_item('language', $this->current_language);
    }

    private function _load_language_files()
    {
        // Load main language file
        $this->lang->load('main', $this->current_language);

        // Load other common language files if they exist
        $common_files = ['form_validation', 'pagination', 'upload', 'email', 'calendar', 'db'];

        foreach ($common_files as $file) {
            if (file_exists(APPPATH . 'language/' . $this->current_language . '/' . $file . '_lang.php')) {
                $this->lang->load($file, $this->current_language);
            }
        }
    }

    private function _set_global_data()
    {
        $this->language_data = array(
            'current_language' => $this->current_language,
            'language_code' => $this->supported_languages[$this->current_language]['code'],
            'language_direction' => $this->supported_languages[$this->current_language]['direction'],
            'language_name' => $this->supported_languages[$this->current_language]['name'],
            'supported_languages' => $this->supported_languages,
            'is_rtl' => ($this->supported_languages[$this->current_language]['direction'] === 'rtl'),
            'is_arabic' => ($this->current_language === 'arabic'),
            'is_english' => ($this->current_language === 'english')
        );
    }

    protected function load_view($view, $data = array(), $return = FALSE)
    {
        // Merge language data with view data
        $data = array_merge($data, $this->language_data);

        // Load view
        return $this->load->view($view, $data, $return);
    }

    protected function get_language_url($page = '')
    {
        $base_url = base_url();

        if ($this->current_language === 'english') {
            return $base_url . 'en/' . $page;
        } else {
            return $base_url . $page;
        }
    }

    protected function switch_language_url($target_language)
    {
        $current_uri = uri_string();

        // Remove language prefix if exists
        if (strpos($current_uri, 'en/') === 0) {
            $current_uri = substr($current_uri, 3);
        }

        // Handle Arabic route mappings
        $arabic_to_english = array(
            'الرئيسية' => '',
            'العروض-العقارية' => 'advertisements',
            'من-نحن' => 'about',
            'اتصل-بنا' => 'contact',
            'الموظفين' => 'staff'
        );

        $english_to_arabic = array(
            '' => 'الرئيسية',
            'advertisements' => 'العروض-العقارية',
            'about' => 'من-نحن',
            'contact' => 'اتصل-بنا',
            'staff' => 'الموظفين'
        );

        // Convert path based on target language
        if ($target_language === 'english') {
            // Convert Arabic path to English
            foreach ($arabic_to_english as $arabic => $english) {
                if ($current_uri === $arabic) {
                    $current_uri = $english;
                    break;
                }
                // Handle property/staff detail pages
                if (strpos($current_uri, $arabic . '/') === 0) {
                    $current_uri = str_replace($arabic . '/', $english . '/', $current_uri);
                    break;
                }
            }
            return base_url('en/' . $current_uri);
        } else {
            // Convert English path to Arabic
            foreach ($english_to_arabic as $english => $arabic) {
                if ($current_uri === $english) {
                    $current_uri = $arabic;
                    break;
                }
                // Handle property/staff detail pages
                if (strpos($current_uri, $english . '/') === 0) {
                    $current_uri = str_replace($english . '/', $arabic . '/', $current_uri);
                    break;
                }
            }
            return base_url($current_uri);
        }
    }
}
