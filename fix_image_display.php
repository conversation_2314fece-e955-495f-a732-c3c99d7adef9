<?php
// Image Display Fix Script
echo "<h1>🔧 Image Display Fix</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

// Step 1: Check and fix directory permissions
echo "<h2>Step 1: Fix Directory Permissions</h2>";

$directories = [
    './assets/',
    './assets/images/',
    './assets/images/properties/'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $current_perms = substr(sprintf('%o', fileperms($dir)), -4);
        echo "📁 $dir - Current permissions: $current_perms ";
        
        if (chmod($dir, 0755)) {
            echo "<span class='success'>✅ Fixed to 755</span><br>";
        } else {
            echo "<span class='error'>❌ Could not change permissions</span><br>";
        }
    } else {
        echo "📁 $dir <span class='error'>❌ Does not exist</span><br>";
        if (mkdir($dir, 0755, true)) {
            echo "&nbsp;&nbsp;&nbsp;<span class='success'>✅ Created directory</span><br>";
        } else {
            echo "&nbsp;&nbsp;&nbsp;<span class='error'>❌ Could not create directory</span><br>";
        }
    }
}

// Step 2: Fix file permissions for existing images
echo "<h2>Step 2: Fix Image File Permissions</h2>";
$properties_dir = './assets/images/properties/';

if (is_dir($properties_dir)) {
    $files = array_diff(scandir($properties_dir), array('.', '..', 'index.html'));
    
    if (count($files) > 0) {
        foreach ($files as $file) {
            $file_path = $properties_dir . $file;
            $current_perms = substr(sprintf('%o', fileperms($file_path)), -4);
            echo "🖼️ $file - Current permissions: $current_perms ";
            
            if (chmod($file_path, 0644)) {
                echo "<span class='success'>✅ Fixed to 644</span><br>";
            } else {
                echo "<span class='error'>❌ Could not change permissions</span><br>";
            }
        }
    } else {
        echo "<span class='warning'>⚠️ No image files found</span><br>";
    }
}

// Step 3: Test image access
echo "<h2>Step 3: Test Image Access</h2>";

$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$path = str_replace(basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['SCRIPT_NAME']);
$base_url = $protocol . '://' . $host . $path;

if (is_dir($properties_dir)) {
    $files = array_diff(scandir($properties_dir), array('.', '..', 'index.html'));
    
    if (count($files) > 0) {
        $test_file = array_values($files)[0];
        $image_url = $base_url . 'assets/images/properties/' . $test_file;
        
        echo "Testing image URL: <a href='$image_url' target='_blank'>$image_url</a><br>";
        echo "<img src='$image_url' style='max-width:200px; border:2px solid #ccc; margin:10px 0;' ";
        echo "onload=\"this.nextSibling.innerHTML='<span class=success>✅ Image loads successfully!</span>';\" ";
        echo "onerror=\"this.nextSibling.innerHTML='<span class=error>❌ Image still not loading</span>';\">";
        echo "<div style='margin-top:5px; font-weight:bold;'>Loading test...</div>";
    }
}

// Step 4: Create test image
echo "<h2>Step 4: Create Test Image</h2>";

if (extension_loaded('gd')) {
    $test_image_path = $properties_dir . 'test_image.png';
    
    // Create a simple test image
    $image = imagecreate(200, 100);
    $bg_color = imagecolorallocate($image, 76, 175, 80); // Green background
    $text_color = imagecolorallocate($image, 255, 255, 255); // White text
    
    imagestring($image, 5, 50, 40, 'TEST IMAGE', $text_color);
    
    if (imagepng($image, $test_image_path)) {
        echo "<span class='success'>✅ Created test image: $test_image_path</span><br>";
        chmod($test_image_path, 0644);
        
        $test_url = $base_url . 'assets/images/properties/test_image.png';
        echo "Test image URL: <a href='$test_url' target='_blank'>$test_url</a><br>";
        echo "<img src='$test_url' style='border:2px solid green; margin:10px 0;'>";
    } else {
        echo "<span class='error'>❌ Could not create test image</span><br>";
    }
    
    imagedestroy($image);
} else {
    echo "<span class='warning'>⚠️ GD extension not available for test image creation</span><br>";
}

// Step 5: Check web server configuration
echo "<h2>Step 5: Web Server Check</h2>";

$server_software = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
echo "Server: $server_software<br>";

// Check if running on Apache
if (strpos($server_software, 'Apache') !== false) {
    echo "<span class='success'>✅ Apache detected</span><br>";
    echo "Make sure mod_rewrite and mod_mime are enabled<br>";
} elseif (strpos($server_software, 'nginx') !== false) {
    echo "<span class='success'>✅ Nginx detected</span><br>";
    echo "Check nginx configuration for static file serving<br>";
} else {
    echo "<span class='warning'>⚠️ Server type: $server_software</span><br>";
}

// Step 6: Alternative image serving method
echo "<h2>Step 6: Alternative Image Serving</h2>";

// Create a simple image serving script
$image_server_content = '<?php
// Simple image server for debugging
$image_file = $_GET["file"] ?? "";

if (empty($image_file)) {
    http_response_code(400);
    die("No file specified");
}

$image_path = "./assets/images/properties/" . basename($image_file);

if (!file_exists($image_path)) {
    http_response_code(404);
    die("Image not found");
}

$mime_type = mime_content_type($image_path);
header("Content-Type: " . $mime_type);
header("Content-Length: " . filesize($image_path));
readfile($image_path);
?>';

if (file_put_contents('serve_image.php', $image_server_content)) {
    echo "<span class='success'>✅ Created alternative image server: serve_image.php</span><br>";
    
    if (is_dir($properties_dir)) {
        $files = array_diff(scandir($properties_dir), array('.', '..', 'index.html'));
        if (count($files) > 0) {
            $test_file = array_values($files)[0];
            $alt_url = $base_url . 'serve_image.php?file=' . $test_file;
            echo "Alternative URL: <a href='$alt_url' target='_blank'>$alt_url</a><br>";
            echo "<img src='$alt_url' style='max-width:200px; border:2px solid blue; margin:10px 0;'>";
        }
    }
} else {
    echo "<span class='error'>❌ Could not create alternative image server</span><br>";
}

// Step 7: Summary and next steps
echo "<h2>Step 7: Summary & Next Steps</h2>";
echo "<div style='background:#e8f5e8; padding:15px; border-left:4px solid #4CAF50;'>";
echo "<strong>What this script did:</strong><br>";
echo "1. ✅ Fixed directory permissions (755)<br>";
echo "2. ✅ Fixed image file permissions (644)<br>";
echo "3. ✅ Updated .htaccess for better image serving<br>";
echo "4. ✅ Created test image and alternative server<br><br>";

echo "<strong>Next steps:</strong><br>";
echo "1. Test the admin panel: <a href='{$base_url}admin' target='_blank'>Admin Dashboard</a><br>";
echo "2. Try adding a new property with images<br>";
echo "3. Check existing property pages for image display<br>";
echo "4. If still not working, use the alternative image server<br>";
echo "</div>";

echo "<h2>🔗 Quick Links</h2>";
echo "• <a href='{$base_url}test_image_access.php' target='_blank'>Detailed Image Test</a><br>";
echo "• <a href='{$base_url}admin/add_property' target='_blank'>Add New Property</a><br>";
echo "• <a href='{$base_url}العروض-العقارية' target='_blank'>Property Listings</a><br>";

// Auto-refresh option
echo "<br><button onclick='location.reload()' style='background:#007bff; color:white; padding:10px 20px; border:none; border-radius:5px;'>🔄 Run Fix Again</button>";
?>
