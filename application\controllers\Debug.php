<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Debug extends CI_Controller {

    public function index()
    {
        echo "<h1>Debug Information</h1>";
        
        echo "<h2>URL Information:</h2>";
        echo "Current URI: " . uri_string() . "<br>";
        echo "Base URL: " . base_url() . "<br>";
        echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";
        echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
        
        echo "<h2>URI Segments:</h2>";
        for ($i = 1; $i <= 5; $i++) {
            echo "Segment $i: " . $this->uri->segment($i) . "<br>";
        }
        
        echo "<h2>Session Data:</h2>";
        echo "Language: " . $this->session->userdata('language') . "<br>";
        
        echo "<h2>Config:</h2>";
        echo "Default Language: " . $this->config->item('default_language') . "<br>";
        echo "Current Language: " . $this->config->item('language') . "<br>";
        
        echo "<h2>Supported Languages:</h2>";
        $supported = $this->config->item('supported_languages');
        foreach ($supported as $key => $lang) {
            echo "$key: " . $lang['name'] . " (" . $lang['code'] . ")<br>";
        }
        
        echo "<h2>Test Arabic Characters:</h2>";
        $arabic_test = 'الرئيسية';
        echo "Arabic Text: " . $arabic_test . "<br>";
        echo "URL Encoded: " . urlencode($arabic_test) . "<br>";
        echo "URL Decoded: " . urldecode(urlencode($arabic_test)) . "<br>";
        
        echo "<h2>Test Links:</h2>";
        echo "<a href='" . base_url() . "'>Default Home</a><br>";
        echo "<a href='" . base_url('الرئيسية') . "'>Arabic Home</a><br>";
        echo "<a href='" . base_url('en') . "'>English Home</a><br>";
        echo "<a href='" . base_url('lang/arabic') . "'>Switch to Arabic</a><br>";
        echo "<a href='" . base_url('lang/english') . "'>Switch to English</a><br>";
        
        echo "<h2>Routes Test:</h2>";
        echo "Testing if routes are working...<br>";
        
        // Test if we can load the language library
        try {
            $this->lang->load('main', 'arabic');
            echo "Arabic language file loaded successfully<br>";
            echo "Site Title: " . $this->lang->line('site_title') . "<br>";
        } catch (Exception $e) {
            echo "Error loading Arabic language file: " . $e->getMessage() . "<br>";
        }
    }
    
    public function test_arabic()
    {
        echo "<h1>Arabic Route Test</h1>";
        echo "This page was accessed via Arabic route<br>";
        echo "Current URI: " . uri_string() . "<br>";
    }
}
?>
