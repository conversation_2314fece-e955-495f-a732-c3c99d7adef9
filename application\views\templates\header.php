<!DOCTYPE html>
<html lang="<?php echo $language_code; ?>" dir="<?php echo $language_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : $this->lang->line('site_title'); ?></title>
    <meta name="description" content="<?php echo isset($meta_description) ? $meta_description : $this->lang->line('meta_description_home'); ?>">
    <meta name="keywords" content="عقارات, الرياض, إدارة أملاك, تأجير, real estate, riyadh, property management, rental">
    <meta name="author" content="<?php echo $this->lang->line('company_name'); ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : $this->lang->line('site_title'); ?>">
    <meta property="og:description" content="<?php echo isset($meta_description) ? $meta_description : $this->lang->line('meta_description_home'); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo current_url(); ?>">
    <meta property="og:image" content="<?php echo base_url('assets/images/logo.png'); ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo base_url('assets/images/favicon.ico'); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <?php if ($is_arabic): ?>
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php endif; ?>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo base_url('assets/css/style.css'); ?>">
    <?php if ($is_rtl): ?>
        <link rel="stylesheet" href="<?php echo base_url('assets/css/rtl.css'); ?>">
    <?php endif; ?>

    <!-- Custom CSS for current page -->
    <?php if (isset($page_class)): ?>
        <link rel="stylesheet" href="<?php echo base_url('assets/css/pages/' . $page_class . '.css'); ?>">
    <?php endif; ?>
</head>
<body class="<?php echo isset($page_class) ? $page_class : ''; ?> <?php echo $language_direction; ?>">

    <!-- Language Switcher -->
    <div class="language-switcher">
        <div class="container">
            <div class="d-flex justify-content-end py-2">
                <?php foreach ($supported_languages as $lang_key => $lang_info): ?>
                    <?php if ($lang_key !== $current_language): ?>
                        <a href="<?php echo base_url('lang/' . $lang_key); ?>"
                           class="language-link text-decoration-none me-2">
                            <i class="fas fa-globe"></i> <?php echo $lang_info['name']; ?>
                        </a>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand d-flex align-items-center" href="<?php echo base_url(); ?>">
                <?php if (file_exists(FCPATH . 'assets/images/logo.png')): ?>
                    <img src="<?php echo base_url('assets/images/logo.png'); ?>"
                         alt="<?php echo $this->lang->line('company_name'); ?>"
                         class="me-2 header-logo-full">
                <?php else: ?>
                    <div class="header-logo-placeholder me-2 d-flex align-items-center justify-content-center"
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-building fa-2x text-white"></i>
                    </div>
                <?php endif; ?>
                <span class="brand-text"><?php echo $this->lang->line('site_title'); ?></span>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav <?php echo $is_rtl ? 'me-auto' : 'ms-auto'; ?>">
                    <li class="nav-item">
                        <a class="nav-link <?php echo (uri_string() == '' || uri_string() == 'home' || ($is_english && uri_string() == 'en')) ? 'active' : ''; ?>"
                           href="<?php echo $is_english ? base_url('en') : base_url(); ?>">
                            <i class="fas fa-home"></i> <?php echo $this->lang->line('nav_home'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos(uri_string(), 'advertisements') !== false || strpos(uri_string(), 'العروض-العقارية') !== false) ? 'active' : ''; ?>"
                           href="<?php echo $is_english ? base_url('en/advertisements') : base_url('العروض-العقارية'); ?>">
                            <i class="fas fa-building"></i> <?php echo $this->lang->line('nav_advertisements'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos(uri_string(), 'about') !== false || strpos(uri_string(), 'من-نحن') !== false) ? 'active' : ''; ?>"
                           href="<?php echo $is_english ? base_url('en/about') : base_url('من-نحن'); ?>">
                            <i class="fas fa-info-circle"></i> <?php echo $this->lang->line('nav_about'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos(uri_string(), 'contact') !== false || strpos(uri_string(), 'اتصل-بنا') !== false) ? 'active' : ''; ?>"
                           href="<?php echo $is_english ? base_url('en/contact') : base_url('اتصل-بنا'); ?>">
                            <i class="fas fa-phone"></i> <?php echo $this->lang->line('nav_contact'); ?>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <?php if ($this->session->flashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show m-0" role="alert">
            <div class="container">
                <i class="fas fa-check-circle"></i> <?php echo $this->session->flashdata('success'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($this->session->flashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show m-0" role="alert">
            <div class="container">
                <i class="fas fa-exclamation-circle"></i> <?php echo $this->session->flashdata('error'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($error) && $error): ?>
        <div class="alert alert-danger alert-dismissible fade show m-0" role="alert">
            <div class="container">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content">
