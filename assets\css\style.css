/* Alwan Al-Yanabea Real Estate - Main Styles */

/* CSS Variables */
:root {
    --primary-color: #093c3c;
    --primary-dark: #072d2d;
    --primary-light: #0b4a4a;
    --secondary-color: #ffffff;
    --text-dark: #333333;
    --text-light: #666666;
    --text-muted: #999999;
    --border-color: #e0e0e0;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --transition: all 0.3s ease;
    --border-radius: 8px;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: #ffffff;
}

body.rtl {
    font-family: 'Noto Sans Arabic', sans-serif;
    direction: rtl;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

.section-title {
    position: relative;
    margin-bottom: 2rem;
    font-weight: 700;
    color: var(--text-dark);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Bootstrap Overrides - Force Green Theme */
.btn-primary,
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    color: white !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-primary.dropdown-toggle:focus {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white !important;
}

.btn-outline-primary,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    background-color: transparent !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active,
.btn-outline-primary.active,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.bg-primary,
.bg-primary:hover,
.bg-primary:focus {
    background-color: var(--primary-color) !important;
}

.text-primary,
.text-primary:hover,
.text-primary:focus {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Badge Overrides */
.badge-primary,
.badge.bg-primary {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Link Overrides */
a {
    color: var(--primary-color);
}

a:hover,
a:focus {
    color: var(--primary-dark);
}

/* Form Controls - Green Theme */
.form-control:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(9, 60, 60, 0.25) !important;
}

.form-select:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(9, 60, 60, 0.25) !important;
}

.form-check-input:checked {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.form-check-input:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(9, 60, 60, 0.25) !important;
}

/* Navigation Overrides */
.navbar-toggler {
    border-color: var(--primary-color) !important;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(9, 60, 60, 0.25) !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%23093c3c' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* Dropdown Overrides */
.dropdown-item:active {
    background-color: var(--primary-color) !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: rgba(9, 60, 60, 0.1) !important;
}

/* Pagination Overrides */
.page-link {
    color: var(--primary-color) !important;
}

.page-link:hover,
.page-link:focus {
    color: var(--primary-dark) !important;
    background-color: rgba(9, 60, 60, 0.1) !important;
    border-color: var(--primary-color) !important;
}

.page-item.active .page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

/* Alert Overrides */
.alert-primary {
    color: var(--primary-dark) !important;
    background-color: rgba(9, 60, 60, 0.1) !important;
    border-color: var(--primary-color) !important;
}

/* Language Switcher */
.language-switcher {
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
}

.language-link {
    color: var(--text-light);
    font-size: 0.9rem;
    transition: var(--transition);
}

.language-link:hover {
    color: var(--primary-color);
}

/* Navigation - Enhanced Professional Design - No Overflow */
.navbar {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    padding: 0.5rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    backdrop-filter: blur(10px);
    border-bottom: 2px solid rgba(255,255,255,0.1);
    overflow-x: hidden;
    width: 100%;
}

.navbar .container {
    max-width: 100%;
    overflow: hidden;
}

.navbar-collapse {
    overflow: hidden;
    max-width: 100%;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    transition: var(--transition);
    color: white !important;
    text-decoration: none !important;
}

.navbar-brand:hover {
    transform: scale(1.05);
    color: white !important;
}

.navbar-brand:focus {
    color: white !important;
}

.brand-text {
    font-size: 1.2rem;
    font-weight: 700;
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    letter-spacing: 0.5px;
    margin-left: 0.5rem;
}

.rtl .brand-text {
    margin-left: 0;
    margin-right: 0.5rem;
}

/* Header Logo - Full Display - WHITE COLOR */
.navbar-brand .header-logo-full {
    max-height: 60px;
    height: auto;
    width: auto;
    max-width: 200px;
    object-fit: contain;
    transition: var(--transition);
    background: none;
    padding: 0;
    border: none;
    /* Force white color on all pages */
    filter: brightness(0) invert(1) brightness(2) contrast(1.5) !important;
    /* Ensure full logo visibility */
    display: block;
}

.navbar-brand:hover .header-logo-full {
    transform: scale(1.05);
    filter: brightness(0) invert(1) brightness(2.5) contrast(1.8) !important;
}

/* Legacy header logo styles for backward compatibility */
.navbar-brand img:not(.header-logo-full),
.navbar-brand .header-logo {
    filter: brightness(0) invert(1) brightness(2) contrast(1.5);
    transition: var(--transition);
    max-height: 50px;
    width: auto;
    background: none;
    padding: 0;
    border: none;
}

.navbar-brand:hover img:not(.header-logo-full),
.navbar-brand:hover .header-logo {
    filter: brightness(0) invert(1) brightness(2.5) contrast(1.8);
    transform: scale(1.05);
}

/* Header logo placeholder styling - Clean White */
.header-logo-placeholder {
    background: none !important;
    border: none !important;
    transition: var(--transition);
}

.navbar-brand:hover .header-logo-placeholder {
    transform: scale(1.05);
}

.navbar-brand:hover .brand-text {
    color: #f0f0f0 !important;
    text-shadow: 2px 2px 6px rgba(0,0,0,0.7);
}

/* Clean Menu Buttons with Box Design */
.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.6rem 0.8rem;
    margin: 0.2rem 0.15rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(255,255,255,0.2) !important;
    color: rgba(255,255,255,0.9) !important;
    text-decoration: none;
    font-size: 0.8rem;
    letter-spacing: 0.3px;
    text-transform: uppercase;
    font-family: 'Inter', sans-serif;
    /* Responsive uniform sizing */
    min-width: 100px;
    max-width: 120px;
    flex: 1;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

/* Arabic Menu Font Size - 2px smaller */
.rtl .navbar-nav .nav-link,
body[dir="rtl"] .navbar-nav .nav-link,
html[lang="ar"] .navbar-nav .nav-link {
    font-size: 0.675rem; /* 2px smaller than 0.8rem */
}

/* Subtle Hover Effects for Box */
.navbar-nav .nav-link:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    color: white !important;
}

/* Active State - Glow Effect Only When Clicked */
.navbar-nav .nav-link.active {
    background: linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    border: 1px solid rgba(255,255,255,0.3);
    color: white !important;
    box-shadow:
        0 0 15px rgba(255,255,255,0.3),
        0 4px 15px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.4);
    transform: translateY(-1px);
    font-weight: 600;
    text-shadow: 0 0 8px rgba(255,255,255,0.6);
}

/* Remove glow effect - keeping clean professional look */

/* Completely Clean Icon Styling */
.navbar-nav .nav-link i {
    margin-right: 0.4rem;
    font-size: 0.9rem;
    transition: none;
    filter: none !important;
    transform: none !important;
    text-shadow: none !important;
}

.rtl .navbar-nav .nav-link i {
    margin-right: 0;
    margin-left: 0.4rem;
}

.navbar-nav .nav-link:hover i {
    /* No hover effects */
    filter: none !important;
    transform: none !important;
}

.navbar-nav .nav-link.active i {
    transform: scale(1.1);
    filter: drop-shadow(0 0 6px rgba(255,255,255,0.7));
}

/* Remove all pseudo-element effects including underlines */
.navbar-nav .nav-link::after,
.navbar-nav .nav-link::before {
    display: none !important;
}

/* Navbar Items Container - No Horizontal Scroll */
.navbar-nav {
    display: flex;
    align-items: center;
    gap: 0.1rem;
    flex-wrap: nowrap;
    overflow: visible;
}

.navbar-nav .nav-item {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

/* Ensure consistent button layout - No Overflow */
@media (min-width: 992px) {
    .navbar-nav {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 0.2rem;
        flex-wrap: nowrap;
        max-width: 100%;
        overflow: hidden;
    }

    .navbar-nav .nav-item {
        flex: 1;
        min-width: 0;
        max-width: 120px;
    }

    .navbar-nav .nav-link {
        flex-shrink: 1;
        min-width: 90px;
        max-width: 110px;
        font-size: 0.75rem;
    }
}

/* Mobile navbar items container */
@media (max-width: 991px) {
    .navbar-nav {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        gap: 0.4rem;
        padding: 1rem 0;
    }

    .navbar-nav .nav-item {
        width: 100%;
        display: flex;
        justify-content: center;
    }
}

/* Enhanced Navbar Brand - FORCE WHITE LOGO ON ALL PAGES */
.navbar-brand img,
.navbar-brand .header-logo-full,
.header .navbar-brand img,
.header .navbar-brand .header-logo-full,
body .navbar-brand img,
body .navbar-brand .header-logo-full {
    filter: brightness(0) invert(1) brightness(2) contrast(1.5) drop-shadow(0 2px 8px rgba(0,0,0,0.3)) !important;
    transition: var(--transition);
}

.navbar-brand:hover img,
.navbar-brand:hover .header-logo-full,
.header .navbar-brand:hover img,
.header .navbar-brand:hover .header-logo-full,
body .navbar-brand:hover img,
body .navbar-brand:hover .header-logo-full {
    filter: brightness(0) invert(1) brightness(2.5) contrast(1.8) drop-shadow(0 4px 12px rgba(0,0,0,0.4)) !important;
}

/* Navbar Collapse Animation */
.navbar-collapse {
    background: rgba(255,255,255,0.05);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    margin-top: 0.5rem;
    padding: 1rem;
    border: 1px solid rgba(255,255,255,0.1);
}

@media (min-width: 992px) {
    .navbar-collapse {
        background: none;
        backdrop-filter: none;
        border: none;
        padding: 0;
        margin-top: 0;
    }
}

/* Smooth Navbar Scroll Effect */
.navbar.scrolled {
    padding: 0.3rem 0;
    background: rgba(9, 60, 60, 0.95) !important;
    backdrop-filter: blur(15px);
    box-shadow: 0 6px 30px rgba(0,0,0,0.25);
}

.navbar.scrolled .navbar-brand {
    transform: scale(0.9);
}

.navbar.scrolled .navbar-nav .nav-link {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
}

/* Professional Underline Effect */
.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 80%;
}

/* Loading Animation for Menu Items */
@keyframes menuItemLoad {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.navbar-nav .nav-item {
    animation: menuItemLoad 0.5s ease forwards;
}

.navbar-nav .nav-item:nth-child(1) { animation-delay: 0.1s; }
.navbar-nav .nav-item:nth-child(2) { animation-delay: 0.2s; }
.navbar-nav .nav-item:nth-child(3) { animation-delay: 0.3s; }
.navbar-nav .nav-item:nth-child(4) { animation-delay: 0.4s; }
.navbar-nav .nav-item:nth-child(5) { animation-delay: 0.5s; }

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    background-image: url('../images/hero-pattern.png');
    background-size: cover;
    background-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(9, 60, 60, 0.8);
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.hero-buttons .btn {
    margin-bottom: 1rem;
}

.hero-image {
    position: relative;
    z-index: 2;
}

.min-vh-75 {
    min-height: 75vh;
}

/* About Section */
.about-section {
    padding: 5rem 0;
}

.about-images img {
    transition: var(--transition);
}

.about-images img:hover {
    transform: scale(1.05);
}

/* Services Section */
.services-section {
    background-color: #f8f9fa;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border: none;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.service-icon {
    transition: var(--transition);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-title {
    color: var(--text-dark);
    font-weight: 600;
}

/* Property Cards */
.property-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border: none;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.property-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.property-card:hover .property-image img {
    transform: scale(1.1);
}

.property-type {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 2;
}

.property-title {
    font-weight: 600;
    color: var(--text-dark);
}

.property-location {
    font-size: 0.9rem;
}

.property-description {
    font-size: 0.9rem;
    line-height: 1.5;
}

.property-price {
    font-weight: 700;
    color: var(--primary-color);
}

/* Statistics Section */
.stats-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.stat-item {
    padding: 1rem;
}

.stat-number {
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    opacity: 0.9;
}

/* Footer */
.footer {
    background-color: #1a1a1a !important;
}

.footer-section h6 {
    color: white;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.footer-title {
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary-color);
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-link {
    color: #cccccc !important;
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
}

.footer-link:hover {
    color: white !important;
    padding-left: 5px;
}

.footer-link:focus {
    color: white !important;
}

.footer-link:active {
    color: white !important;
}

.footer-link:visited {
    color: #cccccc !important;
}

.social-links {
    margin-top: 1rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255,255,255,0.1) !important;
    color: white !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
    backdrop-filter: blur(5px);
}

.social-link:hover {
    background-color: rgba(255,255,255,0.2) !important;
    border-color: rgba(255,255,255,0.6) !important;
    color: white !important;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 4px 15px rgba(255,255,255,0.2);
}

.social-link:focus {
    background-color: rgba(255,255,255,0.15) !important;
    color: white !important;
    outline: none;
    box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
}

.social-link:active {
    background-color: rgba(255,255,255,0.25) !important;
    color: white !important;
    transform: translateY(-1px) scale(1.05);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    font-size: 0.9rem;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(3px);
}

.contact-item i {
    margin-top: 0.2rem;
    flex-shrink: 0;
    color: white !important;
    font-size: 1.1rem;
    text-shadow: 0 0 5px rgba(255,255,255,0.3);
    transition: all 0.3s ease;
}

.contact-item:hover i {
    color: #f0f0f0 !important;
    text-shadow: 0 0 8px rgba(255,255,255,0.5);
    transform: scale(1.1);
}

.text-light-gray {
    color: #cccccc;
}

.footer-divider {
    border-color: #333333;
}

.copyright {
    font-size: 0.9rem;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    z-index: 1000;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .service-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }

    /* Mobile-specific green theme overrides */
    .btn-primary,
    .btn-primary:hover,
    .btn-primary:focus,
    .btn-primary:active,
    .btn-primary.active {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        color: white !important;
    }

    .btn-outline-primary,
    .btn-outline-primary:hover,
    .btn-outline-primary:focus,
    .btn-outline-primary:active,
    .btn-outline-primary.active {
        color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
    }

    .btn-outline-primary:hover,
    .btn-outline-primary:focus,
    .btn-outline-primary:active,
    .btn-outline-primary.active {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    .text-primary {
        color: var(--primary-color) !important;
    }

    .bg-primary {
        background-color: var(--primary-color) !important;
    }

    .badge.bg-primary,
    .badge-primary {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    /* Mobile navigation - Enhanced */
    .navbar-toggler {
        border: 2px solid rgba(255,255,255,0.3) !important;
        border-radius: 8px !important;
        padding: 0.5rem !important;
        background: rgba(255,255,255,0.1) !important;
        backdrop-filter: blur(5px) !important;
        transition: all 0.3s ease !important;
    }

    .navbar-toggler:hover {
        background: rgba(255,255,255,0.2) !important;
        border-color: rgba(255,255,255,0.5) !important;
        transform: scale(1.05) !important;
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.3) !important;
        background: rgba(255,255,255,0.15) !important;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2.5' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
        filter: drop-shadow(0 0 3px rgba(255,255,255,0.3)) !important;
    }

    /* Mobile Menu Items with Box Design */
    .navbar-nav .nav-link {
        margin: 0.4rem auto !important;
        padding: 1rem 1.5rem !important;
        border-radius: 8px !important;
        background: rgba(255,255,255,0.05) !important;
        border: 1px solid rgba(255,255,255,0.2) !important;
        transition: all 0.3s ease !important;
        font-size: 0.9rem !important;
        letter-spacing: 0.3px !important;
        text-transform: uppercase !important;
        font-weight: 500 !important;
        /* Uniform mobile sizing */
        width: 90% !important;
        max-width: 280px !important;
        min-height: 50px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
    }

    /* Arabic Mobile Menu Font Size - 2px smaller */
    .rtl .navbar-nav .nav-link,
    body[dir="rtl"] .navbar-nav .nav-link,
    html[lang="ar"] .navbar-nav .nav-link {
        font-size: 0.775rem !important; /* 2px smaller than 0.9rem */
    }

    .navbar-nav .nav-link:hover {
        background: rgba(255,255,255,0.1) !important;
        border-color: rgba(255,255,255,0.3) !important;
    }

    .navbar-nav .nav-link.active {
        background: linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1)) !important;
        border: 1px solid rgba(255,255,255,0.3) !important;
        box-shadow:
            0 0 15px rgba(255,255,255,0.3),
            0 4px 15px rgba(0,0,0,0.2),
            inset 0 1px 0 rgba(255,255,255,0.4) !important;
        transform: translateX(3px) !important;
        text-shadow: 0 0 8px rgba(255,255,255,0.6) !important;
    }

    .navbar-nav .nav-link i {
        font-size: 1.2rem !important;
        margin-right: 0.8rem !important;
        filter: none !important;
        transform: none !important;
        text-shadow: none !important;
        transition: none !important;
    }

    .rtl .navbar-nav .nav-link i {
        margin-right: 0 !important;
        margin-left: 0.8rem !important;
    }

    .navbar-nav .nav-link:hover i {
        /* No hover effects on mobile */
        filter: none !important;
        transform: none !important;
    }

    .navbar-nav .nav-link.active i {
        transform: scale(1.1) !important;
        filter: drop-shadow(0 0 6px rgba(255,255,255,0.7)) !important;
    }

    /* Mobile navbar brand visibility */
    .navbar-brand {
        color: white !important;
        font-size: 1.1rem !important;
    }

    /* Mobile logo sizing */
    .navbar-brand .header-logo-full {
        max-height: 50px !important;
        max-width: 150px !important;
    }

    .brand-text {
        font-size: 1rem !important;
        margin-left: 0.3rem !important;
        color: white !important;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.7) !important;
        font-weight: 700 !important;
    }

    .rtl .brand-text {
        margin-left: 0 !important;
        margin-right: 0.3rem !important;
    }

    /* Mobile logo override for full display - WHITE COLOR */
    .navbar-brand .header-logo-full {
        max-height: 45px !important;
        max-width: 140px !important;
        filter: brightness(0) invert(1) brightness(2) contrast(1.5) !important;
    }

    /* Legacy mobile logo styles */
    .navbar-brand img:not(.header-logo-full) {
        max-height: 40px !important;
        filter: brightness(0) invert(1) brightness(2) contrast(1.5) !important;
        background: none !important;
        padding: 0 !important;
        border: none !important;
    }
}

@media (max-width: 576px) {
    .hero-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .property-card {
        margin-bottom: 1.5rem;
    }

    .footer-section {
        margin-bottom: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* COMPREHENSIVE GREEN THEME OVERRIDES - FORCE ALL BLUE TO GREEN */
/* This section ensures NO blue colors appear anywhere, especially on mobile */

/* All primary color variations */
.btn-primary,
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-primary.dropdown-toggle:focus {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

.btn-outline-primary,
.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active,
.btn-outline-primary.active,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active,
.btn-outline-primary.active {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* All background and text primary */
.bg-primary,
.bg-primary:hover,
.bg-primary:focus,
.bg-primary:active {
    background-color: var(--primary-color) !important;
}

.text-primary,
.text-primary:hover,
.text-primary:focus,
.text-primary:active {
    color: var(--primary-color) !important;
}

/* All border primary */
.border-primary,
.border-primary:hover,
.border-primary:focus {
    border-color: var(--primary-color) !important;
}

/* All badges */
.badge-primary,
.badge.bg-primary,
.badge.badge-primary {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* All links */
a,
a:hover,
a:focus,
a:active,
a.text-primary,
a.text-primary:hover,
a.text-primary:focus {
    color: var(--primary-color) !important;
}

/* Footer Links Override - Keep White */
.footer .footer-link,
.footer .footer-link:hover,
.footer .footer-link:focus,
.footer .footer-link:active,
.footer .footer-link:visited {
    color: #cccccc !important;
}

.footer .footer-link:hover {
    color: white !important;
}

.footer a.footer-link,
.footer a.footer-link:hover,
.footer a.footer-link:focus,
.footer a.footer-link:active {
    color: #cccccc !important;
}

.footer a.footer-link:hover {
    color: white !important;
}

/* Footer Social Media Icons Override - Keep White */
.footer .social-link,
.footer .social-link:hover,
.footer .social-link:focus,
.footer .social-link:active,
.footer .social-link:visited {
    background-color: rgba(255,255,255,0.1) !important;
    color: white !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
}

.footer .social-link:hover {
    background-color: rgba(255,255,255,0.2) !important;
    border-color: rgba(255,255,255,0.6) !important;
    color: white !important;
    transform: translateY(-3px) scale(1.1) !important;
    box-shadow: 0 4px 15px rgba(255,255,255,0.2) !important;
}

.footer .social-link:focus {
    background-color: rgba(255,255,255,0.15) !important;
    color: white !important;
    box-shadow: 0 0 0 3px rgba(255,255,255,0.3) !important;
}

.footer .social-link:active {
    background-color: rgba(255,255,255,0.25) !important;
    color: white !important;
}

/* Footer Contact Information Icons Override - Keep White */
.footer .contact-item i,
.footer .contact-item i.text-primary,
.footer .contact-item .fas,
.footer .contact-item .fas.text-primary {
    color: white !important;
    text-shadow: 0 0 5px rgba(255,255,255,0.3);
    transition: all 0.3s ease;
}

.footer .contact-item:hover i,
.footer .contact-item:hover i.text-primary,
.footer .contact-item:hover .fas,
.footer .contact-item:hover .fas.text-primary {
    color: #f0f0f0 !important;
    text-shadow: 0 0 8px rgba(255,255,255,0.5);
    transform: scale(1.1);
}

/* Specific contact icon styling */
.footer .fa-map-marker-alt,
.footer .fa-phone,
.footer .fa-mobile-alt,
.footer .fa-envelope {
    color: white !important;
    font-size: 1.1rem;
    margin-right: 0.75rem !important;
}

.rtl .footer .fa-map-marker-alt,
.rtl .footer .fa-phone,
.rtl .footer .fa-mobile-alt,
.rtl .footer .fa-envelope {
    margin-right: 0 !important;
    margin-left: 0.75rem !important;
}

/* Mobile-specific overrides */
@media (max-width: 991px) {
    /* Force all primary colors on mobile */
    .btn-primary,
    .btn-primary:hover,
    .btn-primary:focus,
    .btn-primary:active,
    .btn-primary.active {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        color: white !important;
    }

    .btn-outline-primary {
        color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        background-color: transparent !important;
    }

    .btn-outline-primary:hover,
    .btn-outline-primary:focus,
    .btn-outline-primary:active {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        color: white !important;
    }

    .text-primary {
        color: var(--primary-color) !important;
    }

    .bg-primary {
        background-color: var(--primary-color) !important;
    }

    .badge.bg-primary,
    .badge-primary {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    /* Navigation mobile */
    .navbar-toggler {
        border-color: var(--primary-color) !important;
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(9, 60, 60, 0.25) !important;
    }

    /* Form controls mobile */
    .form-control:focus,
    .form-select:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 0.2rem rgba(9, 60, 60, 0.25) !important;
    }

    .form-check-input:checked {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
    }
}
