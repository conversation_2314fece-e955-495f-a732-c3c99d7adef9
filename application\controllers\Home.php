<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Property_model');
        $this->load->model('Staff_model');
    }

    public function index()
    {
        // Get featured properties
        $data['featured_properties'] = $this->Property_model->get_featured_properties(6);
        
        // Get statistics
        $data['stats'] = array(
            'years_experience' => 30,
            'total_properties' => $this->Property_model->count_all_properties(),
            'total_clients' => 5000
        );
        
        // Page meta data
        $data['page_title'] = $this->lang->line('site_title');
        $data['meta_description'] = $this->lang->line('meta_description_home');
        $data['page_class'] = 'home-page';
        
        // Load views
        $this->load_view('templates/header', $data);
        $this->load_view('home/index', $data);
        $this->load_view('templates/footer', $data);
    }
}
