<?php
// Quick Image System Test
echo "<h1>🔍 Quick Image System Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

// Test 1: Directory Check
echo "<h2>1. Directory Check</h2>";
$upload_dir = './assets/images/properties/';

if (is_dir($upload_dir)) {
    echo "<span class='success'>✅ Directory exists: $upload_dir</span><br>";
    
    if (is_writable($upload_dir)) {
        echo "<span class='success'>✅ Directory is writable</span><br>";
    } else {
        echo "<span class='error'>❌ Directory is NOT writable</span><br>";
        echo "<strong>Fix:</strong> Run: <code>chmod 777 $upload_dir</code><br>";
    }
    
    // List existing images
    $files = array_diff(scandir($upload_dir), array('.', '..', 'index.html'));
    if (count($files) > 0) {
        echo "<span class='success'>✅ Found " . count($files) . " existing images</span><br>";
        foreach ($files as $file) {
            echo "&nbsp;&nbsp;📁 $file<br>";
        }
    } else {
        echo "<span class='warning'>⚠️ No images found in directory</span><br>";
    }
    
} else {
    echo "<span class='error'>❌ Directory does NOT exist: $upload_dir</span><br>";
    echo "<strong>Fix:</strong> Create directory: <code>mkdir -p $upload_dir</code><br>";
}

// Test 2: PHP Upload Settings
echo "<h2>2. PHP Upload Settings</h2>";
$upload_enabled = ini_get('file_uploads');
$max_filesize = ini_get('upload_max_filesize');
$max_post = ini_get('post_max_size');

if ($upload_enabled) {
    echo "<span class='success'>✅ File uploads enabled</span><br>";
} else {
    echo "<span class='error'>❌ File uploads disabled</span><br>";
}

echo "📊 Max file size: $max_filesize<br>";
echo "📊 Max post size: $max_post<br>";

// Test 3: Base URL Test
echo "<h2>3. Base URL Test</h2>";
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$path = str_replace(basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['SCRIPT_NAME']);
$base_url = $protocol . '://' . $host . $path;

echo "🌐 Detected base URL: <strong>$base_url</strong><br>";

// Test 4: Image Access Test
echo "<h2>4. Image Access Test</h2>";
if (is_dir($upload_dir)) {
    $files = array_diff(scandir($upload_dir), array('.', '..', 'index.html'));
    if (count($files) > 0) {
        $test_file = array_values($files)[0];
        $image_url = $base_url . 'assets/images/properties/' . $test_file;
        
        echo "🖼️ Testing image URL: <a href='$image_url' target='_blank'>$image_url</a><br>";
        echo "<img src='$image_url' style='max-width:200px;max-height:150px;border:1px solid #ccc;margin:10px 0;' ";
        echo "onerror=\"this.style.display='none';this.nextSibling.style.display='block';\">";
        echo "<div style='display:none;color:red;font-weight:bold;'>❌ Image failed to load</div>";
    } else {
        echo "<span class='warning'>⚠️ No images to test</span><br>";
    }
}

// Test 5: Quick Upload Test
echo "<h2>5. Quick Upload Test</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['quick_test'])) {
    $upload_file = $_FILES['quick_test'];
    
    if ($upload_file['error'] === 0) {
        $file_ext = pathinfo($upload_file['name'], PATHINFO_EXTENSION);
        $new_name = 'test_' . time() . '.' . $file_ext;
        $target_path = $upload_dir . $new_name;
        
        if (move_uploaded_file($upload_file['tmp_name'], $target_path)) {
            echo "<span class='success'>✅ Upload successful!</span><br>";
            $image_url = $base_url . 'assets/images/properties/' . $new_name;
            echo "🖼️ Uploaded image: <a href='$image_url' target='_blank'>$image_url</a><br>";
            echo "<img src='$image_url' style='max-width:200px;max-height:150px;border:2px solid green;margin:10px 0;'>";
        } else {
            echo "<span class='error'>❌ Upload failed - could not move file</span><br>";
        }
    } else {
        echo "<span class='error'>❌ Upload error: " . $upload_file['error'] . "</span><br>";
    }
}
?>

<form method="post" enctype="multipart/form-data" style="background:#f0f0f0;padding:15px;border-radius:5px;">
    <strong>Test Image Upload:</strong><br>
    <input type="file" name="quick_test" accept="image/*" required>
    <input type="submit" value="Upload Test" style="background:#4CAF50;color:white;padding:5px 15px;border:none;border-radius:3px;">
</form>

<?php
// Test 6: CodeIgniter Integration
echo "<h2>6. CodeIgniter Integration</h2>";

if (file_exists('application/config/config.php')) {
    echo "<span class='success'>✅ CodeIgniter config found</span><br>";
    
    // Check if we can access the admin
    $admin_url = $base_url . 'admin';
    echo "🔗 Admin URL: <a href='$admin_url' target='_blank'>$admin_url</a><br>";
    
} else {
    echo "<span class='error'>❌ CodeIgniter config not found</span><br>";
}

// Test 7: .htaccess Check
echo "<h2>7. .htaccess Check</h2>";
if (file_exists('.htaccess')) {
    echo "<span class='success'>✅ .htaccess file exists</span><br>";
    $htaccess = file_get_contents('.htaccess');
    if (strpos($htaccess, 'assets') !== false) {
        echo "<span class='success'>✅ Assets folder is excluded from rewriting</span><br>";
    } else {
        echo "<span class='warning'>⚠️ Assets folder might be affected by URL rewriting</span><br>";
    }
} else {
    echo "<span class='warning'>⚠️ No .htaccess file found</span><br>";
}

// Summary
echo "<h2>📋 Summary & Next Steps</h2>";
echo "<div style='background:#e8f5e8;padding:15px;border-left:4px solid #4CAF50;'>";
echo "<strong>If all tests pass:</strong><br>";
echo "• Your image system should be working<br>";
echo "• Try uploading images through the admin panel<br>";
echo "• Check property listings for image display<br><br>";

echo "<strong>If tests fail:</strong><br>";
echo "• Fix directory permissions: <code>chmod 777 assets/images/properties/</code><br>";
echo "• Check PHP upload settings in php.ini<br>";
echo "• Verify web server configuration<br>";
echo "• Check browser console for JavaScript errors<br>";
echo "</div>";

echo "<h2>🔗 Quick Links</h2>";
echo "• <a href='{$base_url}admin' target='_blank'>Admin Dashboard</a><br>";
echo "• <a href='{$base_url}admin/add_property' target='_blank'>Add Property</a><br>";
echo "• <a href='{$base_url}العروض-العقارية' target='_blank'>Property Listings</a><br>";
echo "• <a href='{$base_url}assets/images/logo.png' target='_blank'>Test Logo Image</a><br>";
?>

<script>
// Auto-refresh every 30 seconds if testing
setTimeout(function() {
    if (confirm('Refresh page to re-run tests?')) {
        location.reload();
    }
}, 30000);
</script>
