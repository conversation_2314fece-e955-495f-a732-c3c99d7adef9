<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="display-4"><?php echo $this->lang->line('nav_staff'); ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent">
                        <li class="breadcrumb-item">
                            <a href="<?php echo $is_english ? base_url('en') : base_url(); ?>" class="text-white">
                                <?php echo $this->lang->line('nav_home'); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            <?php echo $this->lang->line('nav_staff'); ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Staff Content -->
<section class="staff-content py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title"><?php echo $this->lang->line('our_team'); ?></h2>
                <p class="lead">
                    <?php if ($is_arabic): ?>
                        فريق من المتخصصين المحترفين في مجال العقارات وإدارة الأملاك
                    <?php else: ?>
                        A team of professional specialists in real estate and property management
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <?php if (!empty($staff_members)): ?>
            <div class="row g-4">
                <?php foreach ($staff_members as $member): ?>
                    <div class="col-lg-3 col-md-6">
                        <div class="staff-card text-center h-100">
                            <div class="staff-image mb-3">
                                <?php if (!empty($member->image) && file_exists(FCPATH . 'assets/images/staff/' . $member->image)): ?>
                                    <img src="<?php echo base_url('assets/images/staff/' . $member->image); ?>"
                                         alt="<?php echo $is_arabic ? $member->name_ar : $member->name_en; ?>"
                                         class="img-fluid rounded-circle staff-photo">
                                <?php else: ?>
                                    <div class="staff-placeholder rounded-circle mx-auto d-flex align-items-center justify-content-center"
                                         style="width: 120px; height: 120px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: 4px solid #f8f9fa;">
                                        <i class="fas fa-user fa-3x opacity-75"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="staff-info">
                                <h5 class="staff-name"><?php echo $is_arabic ? $member->name_ar : $member->name_en; ?></h5>
                                <p class="staff-position text-primary mb-2">
                                    <?php echo $is_arabic ? $member->position_ar : $member->position_en; ?>
                                </p>

                                <?php if ($member->experience_years): ?>
                                    <p class="staff-experience text-muted mb-3">
                                        <i class="fas fa-briefcase"></i>
                                        <?php echo $member->experience_years; ?> <?php echo $this->lang->line('years_experience'); ?>
                                    </p>
                                <?php endif; ?>

                                <?php
                                $bio = $is_arabic ? $member->bio_ar : $member->bio_en;
                                if (!empty($bio)):
                                ?>
                                    <p class="staff-bio text-muted mb-3">
                                        <?php echo character_limiter($bio, 100); ?>
                                    </p>
                                <?php endif; ?>

                                <?php
                                $specialties = $is_arabic ? $member->specialties_ar : $member->specialties_en;
                                if (!empty($specialties)):
                                ?>
                                    <div class="staff-specialties mb-3">
                                        <?php
                                        $specialty_list = explode(',', $specialties);
                                        foreach ($specialty_list as $specialty):
                                        ?>
                                            <span class="badge bg-light text-dark me-1 mb-1">
                                                <?php echo trim($specialty); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="staff-contact">
                                    <?php if ($member->phone): ?>
                                        <a href="tel:<?php echo $member->phone; ?>" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                            <i class="fas fa-phone"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if ($member->email): ?>
                                        <a href="mailto:<?php echo $member->email; ?>" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                    <?php endif; ?>

                                    <a href="<?php echo $is_english ? base_url('en/staff/' . $member->id) : base_url('الموظفين/' . $member->id); ?>"
                                       class="btn btn-primary btn-sm mb-2">
                                        <?php echo $this->lang->line('view_details'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="no-staff text-center py-5">
                        <i class="fas fa-users fa-5x text-muted mb-4"></i>
                        <h3>
                            <?php if ($is_arabic): ?>
                                لا توجد معلومات عن الموظفين حالياً
                            <?php else: ?>
                                No staff information available at the moment
                            <?php endif; ?>
                        </h3>
                        <p class="lead text-muted">
                            <?php if ($is_arabic): ?>
                                سيتم إضافة معلومات فريق العمل قريباً
                            <?php else: ?>
                                Staff information will be added soon
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-cta py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h3 class="mb-4">
                    <?php if ($is_arabic): ?>
                        هل تحتاج إلى مساعدة؟
                    <?php else: ?>
                        Need Help?
                    <?php endif; ?>
                </h3>
                <p class="lead mb-4">
                    <?php if ($is_arabic): ?>
                        فريقنا المتخصص جاهز لمساعدتك في جميع احتياجاتك العقارية
                    <?php else: ?>
                        Our specialized team is ready to help you with all your real estate needs
                    <?php endif; ?>
                </p>
                <div class="contact-buttons">
                    <a href="<?php echo $is_english ? base_url('en/contact') : base_url('اتصل-بنا'); ?>"
                       class="btn btn-primary btn-lg me-3 mb-2">
                        <i class="fas fa-phone"></i> <?php echo $this->lang->line('contact_us'); ?>
                    </a>
                    <a href="tel:<?php echo $this->lang->line('company_phone'); ?>"
                       class="btn btn-outline-primary btn-lg mb-2">
                        <i class="fas fa-phone"></i> <?php echo $this->lang->line('company_phone'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.staff-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.staff-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.staff-photo {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border: 4px solid #f8f9fa;
    transition: transform 0.3s ease;
}

.staff-card:hover .staff-photo {
    transform: scale(1.05);
}

.staff-name {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.staff-position {
    font-weight: 500;
    font-size: 1rem;
}

.staff-experience {
    font-size: 0.9rem;
}

.staff-bio {
    font-size: 0.9rem;
    line-height: 1.5;
}

.staff-specialties .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
    border: 1px solid #dee2e6;
}

.staff-contact .btn {
    min-width: 40px;
}

.no-staff {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.contact-cta {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

@media (max-width: 768px) {
    .staff-card {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .staff-photo {
        width: 100px;
        height: 100px;
    }

    .contact-buttons .btn {
        width: 100%;
        margin-bottom: 1rem;
    }
}
</style>
