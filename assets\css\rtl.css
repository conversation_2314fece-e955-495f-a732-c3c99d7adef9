/* RTL Specific Styles for Arabic */

/* Base RTL Adjustments */
body.rtl {
    direction: rtl;
    text-align: right;
}

/* Navigation RTL */
.rtl .navbar-nav {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.rtl .navbar-brand {
    margin-left: auto;
    margin-right: 0;
}

.rtl .navbar-toggler {
    margin-left: 0;
    margin-right: auto;
}

/* Language Switcher RTL */
.rtl .language-switcher .d-flex {
    justify-content-start !important;
}

.rtl .language-link {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Hero Section RTL */
.rtl .hero-content {
    text-align: right;
}

.rtl .hero-buttons .btn {
    margin-left: 1rem;
    margin-right: 0;
}

.rtl .hero-buttons .btn:last-child {
    margin-left: 0;
}

/* Property Cards RTL */
.rtl .property-type {
    right: auto;
    left: 1rem;
}

.rtl .property-location i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Footer RTL */
.rtl .footer-title::after {
    left: auto;
    right: 0;
}

.rtl .footer-link:hover {
    padding-left: 0;
    padding-right: 5px;
}

.rtl .contact-item i {
    margin-left: 0.5rem;
    margin-right: 0;
}

.rtl .social-link {
    margin-left: 0.75rem;
    margin-right: 0;
}

.rtl .social-link:first-child {
    margin-left: 0;
}

/* Back to Top RTL */
.rtl .back-to-top {
    right: auto;
    left: 2rem;
}

/* Section Title RTL */
.rtl .section-title::after {
    left: auto;
    right: 50%;
    transform: translateX(50%);
}

/* Text Alignment RTL */
.rtl .text-center .section-title::after {
    left: 50%;
    right: auto;
    transform: translateX(-50%);
}

/* Bootstrap RTL Overrides */
.rtl .me-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

.rtl .me-3 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
}

.rtl .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

.rtl .me-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

/* Form RTL */
.rtl .form-control {
    text-align: right;
}

.rtl .form-label {
    text-align: right;
}

/* Alert RTL */
.rtl .alert i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Breadcrumb RTL */
.rtl .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    transform: scaleX(-1);
}

/* Pagination RTL */
.rtl .pagination {
    direction: ltr;
}

/* Card RTL */
.rtl .card-body {
    text-align: right;
}

/* List RTL */
.rtl .list-unstyled {
    padding-right: 0;
    padding-left: 0;
}

/* Responsive RTL */
@media (max-width: 768px) {
    .rtl .back-to-top {
        left: 1rem;
        right: auto;
    }
    
    .rtl .hero-buttons .btn {
        margin-left: 0;
        margin-bottom: 0.5rem;
    }
}

/* Animation RTL */
.rtl .slide-in-left {
    animation: slideInRight 0.6s ease-out;
}

.rtl .slide-in-right {
    animation: slideInLeft 0.6s ease-out;
}

/* Custom RTL Utilities */
.rtl .text-start {
    text-align: right !important;
}

.rtl .text-end {
    text-align: left !important;
}

.rtl .float-start {
    float: right !important;
}

.rtl .float-end {
    float: left !important;
}

/* Icon RTL */
.rtl .fas.fa-arrow-left::before {
    content: "\f061"; /* fa-arrow-right */
}

.rtl .fas.fa-arrow-right::before {
    content: "\f060"; /* fa-arrow-left */
}

/* Dropdown RTL */
.rtl .dropdown-menu {
    right: 0;
    left: auto;
}

/* Modal RTL */
.rtl .modal-header .btn-close {
    margin-left: auto;
    margin-right: 0;
}

/* Table RTL */
.rtl .table {
    text-align: right;
}

.rtl .table th,
.rtl .table td {
    text-align: right;
}

/* Progress RTL */
.rtl .progress-bar {
    right: 0;
    left: auto;
}

/* Carousel RTL */
.rtl .carousel-control-prev {
    right: 0;
    left: auto;
}

.rtl .carousel-control-next {
    left: 0;
    right: auto;
}

.rtl .carousel-indicators {
    direction: ltr;
}

/* Tooltip RTL */
.rtl .tooltip {
    direction: rtl;
}

/* Popover RTL */
.rtl .popover {
    direction: rtl;
}

/* Offcanvas RTL */
.rtl .offcanvas-start {
    right: 0;
    left: auto;
    transform: translateX(100%);
}

.rtl .offcanvas-end {
    left: 0;
    right: auto;
    transform: translateX(-100%);
}
