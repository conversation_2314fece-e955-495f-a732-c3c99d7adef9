<?php
// Simple test file to check Arabic URL handling
echo "<h1>Arabic URL Test</h1>";

echo "<h2>Current URL Info:</h2>";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "QUERY_STRING: " . $_SERVER['QUERY_STRING'] . "<br>";
echo "PATH_INFO: " . (isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : 'Not set') . "<br>";

echo "<h2>URL Decoding Test:</h2>";
$test_arabic = 'الرئيسية';
echo "Original Arabic: " . $test_arabic . "<br>";
echo "URL Encoded: " . urlencode($test_arabic) . "<br>";
echo "URL Decoded: " . urldecode(urlencode($test_arabic)) . "<br>";

echo "<h2>Test Links:</h2>";
echo "<a href='http://localhost/alyanabea-website/'>Home (Default)</a><br>";
echo "<a href='http://localhost/alyanabea-website/الرئيسية'>Arabic Home</a><br>";
echo "<a href='http://localhost/alyanabea-website/en'>English Home</a><br>";
echo "<a href='http://localhost/alyanabea-website/lang/arabic'>Switch to Arabic</a><br>";

echo "<h2>Character Encoding:</h2>";
echo "Current encoding: " . mb_internal_encoding() . "<br>";
echo "Detect encoding: " . mb_detect_encoding($test_arabic) . "<br>";
?>
