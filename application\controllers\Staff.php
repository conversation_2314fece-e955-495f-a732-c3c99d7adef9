<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Staff extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Staff_model');
    }

    public function index()
    {
        // Get all staff members
        $data['staff_members'] = $this->Staff_model->get_all_staff();
        
        // Page meta data
        $data['page_title'] = $this->lang->line('nav_staff') . ' - ' . $this->lang->line('site_title');
        $data['meta_description'] = $this->lang->line('our_team') . ' - ' . $this->lang->line('site_title');
        $data['page_class'] = 'staff-page';
        
        // Load views
        $this->load_view('templates/header', $data);
        $this->load_view('staff/index', $data);
        $this->load_view('templates/footer', $data);
    }

    public function view($id)
    {
        $staff_member = $this->Staff_model->get_staff_member($id);
        
        if (!$staff_member) {
            show_404();
        }
        
        // Get staff name in current language
        $staff_name = ($this->current_language === 'arabic') ? $staff_member->name_ar : $staff_member->name_en;
        
        $data['staff_member'] = $staff_member;
        
        // Page meta data
        $data['page_title'] = $staff_name . ' - ' . $this->lang->line('site_title');
        $data['meta_description'] = $staff_name . ' - ' . $this->lang->line('our_team');
        $data['page_class'] = 'staff-detail-page';
        
        // Load views
        $this->load_view('templates/header', $data);
        $this->load_view('staff/view', $data);
        $this->load_view('templates/footer', $data);
    }
}
