<!-- Page Header -->
<section class="page-header bg-primary text-white py-3">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent">
                        <li class="breadcrumb-item">
                            <a href="<?php echo $is_english ? base_url('en') : base_url(); ?>" class="text-white">
                                <?php echo $this->lang->line('nav_home'); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo $is_english ? base_url('en/advertisements') : base_url('العروض-العقارية'); ?>" class="text-white">
                                <?php echo $this->lang->line('nav_advertisements'); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            <?php echo character_limiter($is_arabic ? $property->title_ar : $property->title_en, 30); ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Property Details -->
<section class="property-details py-5">
    <div class="container">
        <div class="row">
            <!-- Property Images -->
            <div class="col-lg-8 mb-4">
                <div class="property-images">
                    <?php
                    // Get gallery images
                    $gallery_images = !empty($property->gallery) ? json_decode($property->gallery, true) : array();
                    // Fix: Use correct path for alyanabea_feb24 project
                    $image_base_path = 'localhost/alyanabea_feb24/assets/images/properties/';
                    $has_main_image = !empty($property->image) && file_exists($image_base_path . $property->image);
                    $has_gallery = !empty($gallery_images);

                    // Debug: Check image file status
                    if (!empty($property->image)) {
                        $image_path = $image_base_path . $property->image;
                        $image_url = base_url('assets/images/properties/' . $property->image);
                        // Uncomment next line for debugging
                        // echo "<!-- Debug: Image file exists: " . (file_exists($image_path) ? 'YES' : 'NO') . " | Path: $image_path | URL: $image_url -->";
                    }
                    print_r($image_base_path); echo"<br>";
                    print_r($image_path);  
                    $total_images = ($has_main_image ? 1 : 0) + count($gallery_images);
                    
                    ?>

                    <?php if ($has_main_image || $has_gallery): ?>
                        <!-- Image Carousel -->
                        <div id="propertyCarousel" class="carousel slide mb-3" data-bs-ride="carousel">
                            <div class="carousel-inner rounded shadow">
                                <?php $slide_index = 0; ?>

                                <!-- Main Image -->
                                <?php if ($has_main_image): ?>
                                    <div class="carousel-item <?php echo $slide_index === 0 ? 'active' : ''; ?>">
                                        <img src="<?php echo base_url('localhost/alyanabea_feb24/assets/images/properties/' . $property->image); ?>"
                                             alt="<?php echo $is_arabic ? $property->title_ar : $property->title_en; ?>"
                                             class="d-block w-100" style="height: 400px; object-fit: cover;">
                                        <div class="carousel-caption d-none d-md-block bg-dark bg-opacity-50 rounded">
                                            <h5><?php echo $is_arabic ? 'الصورة الرئيسية' : 'Main Image'; ?></h5>
                                        </div>
                                    </div>
                                    <?php $slide_index++; ?>
                                <?php endif; ?>

                                <!-- Gallery Images -->
                                <?php if ($has_gallery): ?>
                                    <?php foreach ($gallery_images as $index => $gallery_image): ?>
                                        <?php if (file_exists($image_base_path . $gallery_image)): ?>
                                            <div class="carousel-item <?php echo $slide_index === 0 ? 'active' : ''; ?>">
                                                <img src="<?php echo base_url('localhost/alyanabea_feb24/assets/images/properties/' . $gallery_image); ?>"
                                                     alt="<?php echo ($is_arabic ? $property->title_ar : $property->title_en) . ' - ' . ($is_arabic ? 'صورة' : 'Image') . ' ' . ($index + 1); ?>"
                                                     class="d-block w-100" style="height: 400px; object-fit: cover;">
                                                <div class="carousel-caption d-none d-md-block bg-dark bg-opacity-50 rounded">
                                                    <h5><?php echo $is_arabic ? 'صورة ' . ($index + 1) : 'Image ' . ($index + 1); ?></h5>
                                                </div>
                                            </div>
                                            <?php $slide_index++; ?>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>

                            <!-- Carousel Controls -->
                            <?php if ($total_images > 1): ?>
                                <button class="carousel-control-prev" type="button" data-bs-target="#propertyCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden"><?php echo $is_arabic ? 'السابق' : 'Previous'; ?></span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#propertyCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden"><?php echo $is_arabic ? 'التالي' : 'Next'; ?></span>
                                </button>

                                <!-- Image Counter -->
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-dark bg-opacity-75 fs-6">
                                        <i class="fas fa-images me-1"></i>
                                        <span id="current-slide">1</span> / <?php echo $total_images; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Thumbnail Navigation -->
                        <?php if ($total_images > 1): ?>
                            <div class="property-thumbnails">
                                <div class="row g-2">
                                    <?php $thumb_index = 0; ?>

                                    <!-- Main Image Thumbnail -->
                                    <?php if ($has_main_image): ?>
                                        <div class="col-2">
                                            <img src="<?php echo base_url('localhost/alyanabea_feb24/assets/images/properties/' . $property->image); ?>"
                                                 alt="Main"
                                                 class="img-fluid rounded thumbnail-img <?php echo $thumb_index === 0 ? 'active' : ''; ?>"
                                                 style="height: 60px; object-fit: cover; cursor: pointer; border: 2px solid transparent;"
                                                 data-bs-target="#propertyCarousel" data-bs-slide-to="<?php echo $thumb_index; ?>">
                                        </div>
                                        <?php $thumb_index++; ?>
                                    <?php endif; ?>

                                    <!-- Gallery Thumbnails -->
                                    <?php if ($has_gallery): ?>
                                        <?php foreach ($gallery_images as $gallery_image): ?>
                                            <?php if (file_exists($image_base_path . $gallery_image)): ?>
                                                <div class="col-2">
                                                    <img src="<?php echo base_url('localhost/alyanabea_feb24/assets/images/properties/' . $gallery_image); ?>"
                                                         alt="Gallery"
                                                         class="img-fluid rounded thumbnail-img"
                                                         style="height: 60px; object-fit: cover; cursor: pointer; border: 2px solid transparent;"
                                                         data-bs-target="#propertyCarousel" data-bs-slide-to="<?php echo $thumb_index; ?>">
                                                </div>
                                                <?php $thumb_index++; ?>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <!-- No Images Placeholder -->
                        <div class="property-placeholder rounded shadow"
                             style="width: 100%; height: 400px; background: url('https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80') center/cover; position: relative;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(9, 60, 60, 0.7); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                                <div class="text-center">
                                    <i class="fas fa-home fa-4x mb-3 opacity-75"></i>
                                    <h4><?php echo $is_arabic ? 'صورة العقار' : 'Property Image'; ?></h4>
                                    <p class="mb-0 opacity-75"><?php echo $is_arabic ? 'لا توجد صورة متاحة' : 'No image available'; ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Property Badges -->
                    <div class="property-badges mb-3">
                        <span class="badge bg-primary fs-6 me-2">
                            <?php
                            if ($is_arabic) {
                                echo ($property->type == 'residential') ? 'سكني' :
                                     (($property->type == 'commercial') ? 'تجاري' : 'إداري');
                            } else {
                                echo ucfirst($property->type);
                            }
                            ?>
                        </span>
                        <?php if ($property->featured): ?>
                            <span class="badge bg-warning text-dark fs-6 me-2">
                                <?php echo $is_arabic ? 'مميز' : 'Featured'; ?>
                            </span>
                        <?php endif; ?>
                        <span class="badge bg-success fs-6">
                            <?php echo number_format($property->price); ?> <?php echo $is_arabic ? 'ريال' : 'SAR'; ?>
                        </span>
                    </div>
                </div>

                <!-- Property Information -->
                <div class="property-info">
                    <h1 class="mb-3"><?php echo $is_arabic ? $property->title_ar : $property->title_en; ?></h1>

                    <div class="property-meta mb-4">
                        <p class="text-muted mb-2">
                            <i class="fas fa-map-marker-alt"></i>
                            <?php echo $is_arabic ? $property->location_ar : $property->location_en; ?>
                        </p>

                        <div class="property-features">
                            <?php if ($property->area): ?>
                                <span class="feature-item me-3">
                                    <i class="fas fa-ruler-combined"></i>
                                    <?php echo $property->area; ?> <?php echo $is_arabic ? 'متر مربع' : 'sq m'; ?>
                                </span>
                            <?php endif; ?>

                            <?php if ($property->bedrooms): ?>
                                <span class="feature-item me-3">
                                    <i class="fas fa-bed"></i>
                                    <?php echo $property->bedrooms; ?> <?php echo $is_arabic ? 'غرف نوم' : 'Bedrooms'; ?>
                                </span>
                            <?php endif; ?>

                            <?php if ($property->bathrooms): ?>
                                <span class="feature-item me-3">
                                    <i class="fas fa-bath"></i>
                                    <?php echo $property->bathrooms; ?> <?php echo $is_arabic ? 'حمامات' : 'Bathrooms'; ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="property-description">
                        <h3><?php echo $this->lang->line('property_details'); ?></h3>
                        <p><?php echo nl2br($is_arabic ? $property->description_ar : $property->description_en); ?></p>
                    </div>

                    <!-- Features -->
                    <?php
                    $features = $is_arabic ? $property->features_ar : $property->features_en;
                    if (!empty($features)):
                    ?>
                        <div class="property-features-list mt-4">
                            <h3>
                                <?php if ($is_arabic): ?>
                                    المميزات والخدمات
                                <?php else: ?>
                                    Features & Amenities
                                <?php endif; ?>
                            </h3>
                            <div class="row">
                                <?php
                                $feature_list = explode(',', $features);
                                foreach ($feature_list as $feature):
                                ?>
                                    <div class="col-md-6 mb-2">
                                        <i class="fas fa-check text-success"></i> <?php echo trim($feature); ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Contact Agent -->
                <div class="contact-agent-card mb-4">
                    <h5 class="card-title">
                        <?php if ($is_arabic): ?>
                            تواصل مع المكتب
                        <?php else: ?>
                            Contact Office
                        <?php endif; ?>
                    </h5>
                    <div class="agent-info text-center mb-3">
                        <?php if (file_exists(FCPATH . 'assets/images/logo.png')): ?>
                            <img src="<?php echo base_url('assets/images/logo.png'); ?>"
                                 alt="<?php echo $this->lang->line('company_name'); ?>"
                                 class="rounded mb-2" style="width: 80px; height: 80px; object-fit: cover;">
                        <?php else: ?>
                            <div class="logo-placeholder rounded mb-2 mx-auto"
                                 style="width: 80px; height: 80px; background: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80') center/cover; position: relative;">
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(9, 60, 60, 0.8); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                                    <i class="fas fa-building fa-2x"></i>
                                </div>
                            </div>
                        <?php endif; ?>
                        <h6><?php echo $this->lang->line('company_name'); ?></h6>
                        <p class="text-muted small">
                            <?php if ($is_arabic): ?>
                                مكتب عقاري مرخص
                            <?php else: ?>
                                Licensed Real Estate Office
                            <?php endif; ?>
                        </p>
                    </div>

                    <div class="contact-buttons d-grid gap-2">
                        <a href="tel:<?php echo $this->lang->line('company_phone'); ?>" class="btn btn-primary">
                            <i class="fas fa-phone"></i>
                            <?php if ($is_arabic): ?>
                                اتصل الآن
                            <?php else: ?>
                                Call Now
                            <?php endif; ?>
                        </a>
                        <a href="tel:<?php echo $this->lang->line('company_mobile'); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-mobile-alt"></i>
                            <?php if ($is_arabic): ?>
                                جوال
                            <?php else: ?>
                                Mobile
                            <?php endif; ?>
                        </a>
                        <a href="mailto:<?php echo $this->lang->line('company_email'); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-envelope"></i>
                            <?php if ($is_arabic): ?>
                                إرسال إيميل
                            <?php else: ?>
                                Send Email
                            <?php endif; ?>
                        </a>
                        <a href="<?php echo $is_english ? base_url('en/contact') : base_url('اتصل-بنا'); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-calendar"></i>
                            <?php if ($is_arabic): ?>
                                حجز موعد
                            <?php else: ?>
                                Schedule Visit
                            <?php endif; ?>
                        </a>
                    </div>
                </div>

                <!-- Property Details -->
                <div class="property-details-card mb-4">
                    <h5 class="card-title"><?php echo $this->lang->line('property_details'); ?></h5>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong><?php echo $this->lang->line('property_type'); ?>:</strong></td>
                            <td>
                                <?php
                                if ($is_arabic) {
                                    echo ($property->type == 'residential') ? 'سكني' :
                                         (($property->type == 'commercial') ? 'تجاري' : 'إداري');
                                } else {
                                    echo ucfirst($property->type);
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td><strong><?php echo $this->lang->line('property_price'); ?>:</strong></td>
                            <td class="text-success fw-bold">
                                <?php echo number_format($property->price); ?> <?php echo $is_arabic ? 'ريال' : 'SAR'; ?>
                            </td>
                        </tr>
                        <?php if ($property->area): ?>
                        <tr>
                            <td><strong><?php echo $this->lang->line('property_area'); ?>:</strong></td>
                            <td><?php echo $property->area; ?> <?php echo $is_arabic ? 'م²' : 'm²'; ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if ($property->bedrooms): ?>
                        <tr>
                            <td><strong><?php echo $is_arabic ? 'غرف النوم' : 'Bedrooms'; ?>:</strong></td>
                            <td><?php echo $property->bedrooms; ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if ($property->bathrooms): ?>
                        <tr>
                            <td><strong><?php echo $is_arabic ? 'الحمامات' : 'Bathrooms'; ?>:</strong></td>
                            <td><?php echo $property->bathrooms; ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <td><strong><?php echo $is_arabic ? 'تاريخ الإضافة' : 'Listed'; ?>:</strong></td>
                            <td><?php echo date('M d, Y', strtotime($property->created_at)); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Properties -->
<?php if (!empty($related_properties)): ?>
<section class="related-properties py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="mb-4">
                    <?php if ($is_arabic): ?>
                        عقارات مشابهة
                    <?php else: ?>
                        Similar Properties
                    <?php endif; ?>
                </h3>
            </div>
        </div>

        <div class="row g-4">
            <?php foreach ($related_properties as $related): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="property-card h-100">
                        <div class="property-image position-relative">
                            <?php if (!empty($related->image) && file_exists($image_base_path . $related->image)): ?>
                                <img src="<?php echo base_url('assets/images/properties/' . $related->image); ?>"
                                     alt="<?php echo $is_arabic ? $related->title_ar : $related->title_en; ?>"
                                     class="img-fluid" style="height: 200px; object-fit: cover; width: 100%;">
                            <?php else: ?>
                                <div class="property-placeholder"
                                     style="height: 200px; width: 100%; background: url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80') center/cover; position: relative;">
                                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(9, 60, 60, 0.7); display: flex; align-items: center; justify-content: center; color: white;">
                                        <div class="text-center">
                                            <i class="fas fa-home fa-2x mb-2 opacity-75"></i>
                                            <p class="mb-0 small opacity-75"><?php echo $is_arabic ? 'لا توجد صورة' : 'No image'; ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <span class="badge bg-primary position-absolute top-0 end-0 m-2">
                                <?php
                                if ($is_arabic) {
                                    echo ($related->type == 'residential') ? 'سكني' :
                                         (($related->type == 'commercial') ? 'تجاري' : 'إداري');
                                } else {
                                    echo ucfirst($related->type);
                                }
                                ?>
                            </span>
                        </div>
                        <div class="property-content p-3">
                            <h6 class="property-title"><?php echo character_limiter($is_arabic ? $related->title_ar : $related->title_en, 40); ?></h6>
                            <p class="property-location text-muted small">
                                <i class="fas fa-map-marker-alt"></i>
                                <?php echo $is_arabic ? $related->location_ar : $related->location_en; ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-success fw-bold">
                                    <?php echo number_format($related->price); ?> <?php echo $is_arabic ? 'ريال' : 'SAR'; ?>
                                </span>
                                <a href="<?php echo base_url('advertisements/' . $related->id); ?>" class="btn btn-sm btn-outline-primary">
                                    <?php echo $is_arabic ? 'عرض' : 'View'; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<style>
.contact-agent-card,
.property-details-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: none;
}

.feature-item {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.property-features-list .fas.fa-check {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.rtl .property-features-list .fas.fa-check {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Property Gallery Styles */
.thumbnail-img {
    transition: all 0.3s ease;
}

.thumbnail-img:hover {
    border-color: var(--primary-color) !important;
    transform: scale(1.05);
}

.thumbnail-img.active {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    padding: 20px;
}

.property-thumbnails {
    max-height: 80px;
    overflow-x: auto;
    overflow-y: hidden;
}

.property-thumbnails .row {
    flex-wrap: nowrap;
}

@media (max-width: 768px) {
    .property-thumbnails .col-2 {
        min-width: 80px;
        flex: 0 0 auto;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Property carousel functionality
    const carousel = document.getElementById('propertyCarousel');
    const thumbnails = document.querySelectorAll('.thumbnail-img');
    const currentSlideSpan = document.getElementById('current-slide');

    if (carousel && thumbnails.length > 0) {
        // Update current slide counter
        carousel.addEventListener('slide.bs.carousel', function(e) {
            const activeIndex = e.to + 1;
            if (currentSlideSpan) {
                currentSlideSpan.textContent = activeIndex;
            }

            // Update thumbnail active state
            thumbnails.forEach((thumb, index) => {
                thumb.classList.toggle('active', index === e.to);
            });
        });

        // Thumbnail click handlers
        thumbnails.forEach((thumbnail, index) => {
            thumbnail.addEventListener('click', function() {
                const carouselInstance = bootstrap.Carousel.getOrCreateInstance(carousel);
                carouselInstance.to(index);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                const carouselInstance = bootstrap.Carousel.getOrCreateInstance(carousel);
                carouselInstance.prev();
            } else if (e.key === 'ArrowRight') {
                const carouselInstance = bootstrap.Carousel.getOrCreateInstance(carousel);
                carouselInstance.next();
            }
        });

        // Touch/swipe support for mobile
        let startX = 0;
        let endX = 0;

        carousel.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });

        carousel.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            const diff = startX - endX;

            if (Math.abs(diff) > 50) { // Minimum swipe distance
                const carouselInstance = bootstrap.Carousel.getOrCreateInstance(carousel);
                if (diff > 0) {
                    carouselInstance.next(); // Swipe left - next image
                } else {
                    carouselInstance.prev(); // Swipe right - previous image
                }
            }
        });
    }

    // Auto-scroll thumbnails to keep active thumbnail visible
    function scrollToActiveThumbnail() {
        const activeThumbnail = document.querySelector('.thumbnail-img.active');
        const thumbnailContainer = document.querySelector('.property-thumbnails');

        if (activeThumbnail && thumbnailContainer) {
            const containerRect = thumbnailContainer.getBoundingClientRect();
            const thumbnailRect = activeThumbnail.getBoundingClientRect();

            if (thumbnailRect.left < containerRect.left || thumbnailRect.right > containerRect.right) {
                activeThumbnail.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            }
        }
    }

    // Call scroll function when carousel changes
    if (carousel) {
        carousel.addEventListener('slid.bs.carousel', scrollToActiveThumbnail);
    }
});
</script>
