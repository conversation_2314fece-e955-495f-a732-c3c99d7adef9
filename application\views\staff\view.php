<!-- <PERSON> Header -->
<section class="page-header bg-primary text-white py-3">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent">
                        <li class="breadcrumb-item">
                            <a href="<?php echo $is_english ? base_url('en') : base_url(); ?>" class="text-white">
                                <?php echo $this->lang->line('nav_home'); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo $is_english ? base_url('en/staff') : base_url('الموظفين'); ?>" class="text-white">
                                <?php echo $this->lang->line('nav_staff'); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            <?php echo $is_arabic ? $staff_member->name_ar : $staff_member->name_en; ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Staff Details -->
<section class="staff-details py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <!-- Staff Photo -->
                <div class="staff-photo-card text-center">
                    <img src="<?php echo !empty($staff_member->image) ? base_url('assets/images/staff/' . $staff_member->image) : base_url('assets/images/staff-placeholder.jpg'); ?>" 
                         alt="<?php echo $is_arabic ? $staff_member->name_ar : $staff_member->name_en; ?>" 
                         class="img-fluid rounded-circle staff-photo mb-3">
                    
                    <h2 class="staff-name"><?php echo $is_arabic ? $staff_member->name_ar : $staff_member->name_en; ?></h2>
                    <p class="staff-position text-primary h5 mb-3">
                        <?php echo $is_arabic ? $staff_member->position_ar : $staff_member->position_en; ?>
                    </p>
                    
                    <?php if ($staff_member->experience_years): ?>
                        <div class="experience-badge mb-3">
                            <span class="badge bg-primary fs-6">
                                <i class="fas fa-briefcase"></i> 
                                <?php echo $staff_member->experience_years; ?> <?php echo $this->lang->line('years_experience'); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Contact Information -->
                    <div class="staff-contact">
                        <?php if ($staff_member->phone): ?>
                            <a href="tel:<?php echo $staff_member->phone; ?>" class="btn btn-primary btn-lg mb-2 w-100">
                                <i class="fas fa-phone"></i> 
                                <?php if ($is_arabic): ?>
                                    اتصل الآن
                                <?php else: ?>
                                    Call Now
                                <?php endif; ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($staff_member->email): ?>
                            <a href="mailto:<?php echo $staff_member->email; ?>" class="btn btn-outline-primary btn-lg mb-2 w-100">
                                <i class="fas fa-envelope"></i> 
                                <?php if ($is_arabic): ?>
                                    إرسال إيميل
                                <?php else: ?>
                                    Send Email
                                <?php endif; ?>
                            </a>
                        <?php endif; ?>
                        
                        <a href="<?php echo $is_english ? base_url('en/contact') : base_url('اتصل-بنا'); ?>" 
                           class="btn btn-outline-secondary btn-lg w-100">
                            <i class="fas fa-calendar"></i> 
                            <?php if ($is_arabic): ?>
                                حجز موعد
                            <?php else: ?>
                                Schedule Meeting
                            <?php endif; ?>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-8">
                <!-- Staff Information -->
                <div class="staff-info-card">
                    <h3 class="mb-4">
                        <?php if ($is_arabic): ?>
                            نبذة عن <?php echo $staff_member->name_ar; ?>
                        <?php else: ?>
                            About <?php echo $staff_member->name_en; ?>
                        <?php endif; ?>
                    </h3>
                    
                    <?php 
                    $bio = $is_arabic ? $staff_member->bio_ar : $staff_member->bio_en;
                    if (!empty($bio)): 
                    ?>
                        <div class="staff-bio mb-4">
                            <p class="lead"><?php echo nl2br($bio); ?></p>
                        </div>
                    <?php else: ?>
                        <div class="staff-bio mb-4">
                            <p class="lead">
                                <?php if ($is_arabic): ?>
                                    <?php echo $staff_member->name_ar; ?> هو عضو متميز في فريق عمل مكتب ألوان الينابيع للعقارات، 
                                    يتمتع بخبرة واسعة في مجال العقارات وإدارة الأملاك.
                                <?php else: ?>
                                    <?php echo $staff_member->name_en; ?> is a distinguished member of the Alwan Al-Yanabea Real Estate team, 
                                    with extensive experience in real estate and property management.
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Specialties -->
                    <?php 
                    $specialties = $is_arabic ? $staff_member->specialties_ar : $staff_member->specialties_en;
                    if (!empty($specialties)): 
                    ?>
                        <div class="staff-specialties mb-4">
                            <h4 class="mb-3">
                                <?php if ($is_arabic): ?>
                                    التخصصات
                                <?php else: ?>
                                    Specialties
                                <?php endif; ?>
                            </h4>
                            <div class="specialties-list">
                                <?php 
                                $specialty_list = explode(',', $specialties);
                                foreach ($specialty_list as $specialty): 
                                ?>
                                    <span class="badge bg-light text-dark me-2 mb-2 fs-6">
                                        <i class="fas fa-check text-success"></i> <?php echo trim($specialty); ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Professional Details -->
                    <div class="professional-details">
                        <h4 class="mb-3">
                            <?php if ($is_arabic): ?>
                                المعلومات المهنية
                            <?php else: ?>
                                Professional Information
                            <?php endif; ?>
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <strong>
                                        <?php if ($is_arabic): ?>
                                            المنصب:
                                        <?php else: ?>
                                            Position:
                                        <?php endif; ?>
                                    </strong>
                                    <p><?php echo $is_arabic ? $staff_member->position_ar : $staff_member->position_en; ?></p>
                                </div>
                            </div>
                            
                            <?php if ($staff_member->experience_years): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="detail-item">
                                        <strong>
                                            <?php if ($is_arabic): ?>
                                                سنوات الخبرة:
                                            <?php else: ?>
                                                Years of Experience:
                                            <?php endif; ?>
                                        </strong>
                                        <p><?php echo $staff_member->experience_years; ?> <?php echo $this->lang->line('years_experience'); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($staff_member->email): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="detail-item">
                                        <strong>
                                            <?php if ($is_arabic): ?>
                                                البريد الإلكتروني:
                                            <?php else: ?>
                                                Email:
                                            <?php endif; ?>
                                        </strong>
                                        <p><a href="mailto:<?php echo $staff_member->email; ?>"><?php echo $staff_member->email; ?></a></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($staff_member->phone): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="detail-item">
                                        <strong>
                                            <?php if ($is_arabic): ?>
                                                رقم الهاتف:
                                            <?php else: ?>
                                                Phone:
                                            <?php endif; ?>
                                        </strong>
                                        <p><a href="tel:<?php echo $staff_member->phone; ?>"><?php echo $staff_member->phone; ?></a></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Back to Team -->
<section class="back-to-team py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <a href="<?php echo $is_english ? base_url('en/staff') : base_url('الموظفين'); ?>" 
                   class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> 
                    <?php if ($is_arabic): ?>
                        العودة إلى فريق العمل
                    <?php else: ?>
                        Back to Team
                    <?php endif; ?>
                </a>
            </div>
        </div>
    </div>
</section>

<style>
.staff-photo-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: none;
}

.staff-info-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: none;
    height: fit-content;
}

.staff-photo {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border: 5px solid #f8f9fa;
}

.staff-name {
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.staff-position {
    font-weight: 600;
}

.experience-badge .badge {
    padding: 0.75rem 1rem;
}

.staff-contact .btn {
    font-weight: 500;
}

.staff-bio {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-light);
}

.specialties-list .badge {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    border: 1px solid #dee2e6;
}

.detail-item strong {
    color: var(--text-dark);
    display: block;
    margin-bottom: 0.25rem;
}

.detail-item p {
    color: var(--text-light);
    margin-bottom: 0;
}

.detail-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.detail-item a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .staff-photo-card,
    .staff-info-card {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .staff-photo {
        width: 150px;
        height: 150px;
    }
    
    .staff-contact .btn {
        margin-bottom: 1rem;
    }
}
</style>
