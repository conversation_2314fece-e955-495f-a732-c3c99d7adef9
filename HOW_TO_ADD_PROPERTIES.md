# How to Add Properties to Alyanabea Website

## 📋 **Property Management Guide**

### **🎯 Overview**
The Alyanabea website supports bilingual (Arabic/English) property listings with full management capabilities. Properties can be added through database insertion or by creating an admin panel.

---

## **🗄️ Database Structure**

### **Properties Table Fields:**

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `title_ar` | VARCHAR(255) | ✅ | Arabic property title |
| `title_en` | VARCHAR(255) | ✅ | English property title |
| `description_ar` | TEXT | ✅ | Arabic description |
| `description_en` | TEXT | ✅ | English description |
| `type` | ENUM | ✅ | residential/commercial/administrative |
| `type_ar` | VARCHAR(100) | ❌ | Arabic type display |
| `price` | DECIMAL(12,2) | ✅ | Property price |
| `currency` | VARCHAR(10) | ❌ | Default: SAR |
| `location_ar` | VARCHAR(255) | ✅ | Arabic location |
| `location_en` | VARCHAR(255) | ✅ | English location |
| `address_ar` | TEXT | ❌ | Detailed Arabic address |
| `address_en` | TEXT | ❌ | Detailed English address |
| `area` | INT | ❌ | Area in square meters |
| `bedrooms` | INT | ❌ | Number of bedrooms |
| `bathrooms` | INT | ❌ | Number of bathrooms |
| `features_ar` | TEXT | ❌ | Comma-separated Arabic features |
| `features_en` | TEXT | ❌ | Comma-separated English features |
| `image` | VARCHAR(255) | ❌ | Main image filename |
| `gallery` | TEXT | ❌ | JSON array of image paths |
| `featured` | TINYINT(1) | ❌ | 1 for featured, 0 for normal |
| `status` | ENUM | ❌ | active/sold/rented/inactive |
| `views` | INT | ❌ | View counter |

---

## **📝 Method 1: Direct Database Insert**

### **SQL Example:**
```sql
INSERT INTO `properties` (
    `title_ar`, `title_en`,
    `description_ar`, `description_en`,
    `type`, `type_ar`,
    `price`, `currency`,
    `location_ar`, `location_en`,
    `area`, `bedrooms`, `bathrooms`,
    `features_ar`, `features_en`,
    `featured`, `status`
) VALUES (
    'شقة حديثة في حي الملز',
    'Modern Apartment in Al-Malaz District',
    'شقة حديثة مكونة من 3 غرف نوم وصالة ومطبخ مجهز',
    'Modern apartment with 3 bedrooms, living room and equipped kitchen',
    'residential',
    'سكني',
    750000.00,
    'SAR',
    'حي الملز، الرياض',
    'Al-Malaz District, Riyadh',
    160,
    3,
    2,
    'مطبخ مجهز,موقف سيارة,مصعد,أمن',
    'Equipped Kitchen,Parking,Elevator,Security',
    1,
    'active'
);
```

---

## **🖼️ Method 2: Adding Property Images**

### **Image Upload Process:**

1. **Create Images Directory:**
   ```
   assets/images/properties/
   ```

2. **Image Naming Convention:**
   ```
   property_[ID]_main.jpg        (Main image)
   property_[ID]_1.jpg           (Gallery image 1)
   property_[ID]_2.jpg           (Gallery image 2)
   ```

3. **Update Database with Images:**
   ```sql
   UPDATE properties
   SET image = 'property_1_main.jpg',
       gallery = '["property_1_1.jpg","property_1_2.jpg","property_1_3.jpg"]'
   WHERE id = 1;
   ```

---

## **⚡ Method 3: Quick Add Script**

### **Create: `add_property.php`**
```php
<?php
// Database connection
$host = 'localhost';
$dbname = 'alyanabea_db';
$username = 'your_username';
$password = 'your_password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Property data
    $properties = [
        [
            'title_ar' => 'شقة فاخرة في حي الملقا',
            'title_en' => 'Luxury Apartment in Al-Malqa',
            'description_ar' => 'شقة فاخرة مكونة من 3 غرف نوم وصالة ومطبخ مجهز',
            'description_en' => 'Luxury apartment with 3 bedrooms, living room and equipped kitchen',
            'type' => 'residential',
            'type_ar' => 'سكني',
            'price' => 850000.00,
            'location_ar' => 'حي الملقا، الرياض',
            'location_en' => 'Al-Malqa District, Riyadh',
            'area' => 180,
            'bedrooms' => 3,
            'bathrooms' => 2,
            'features_ar' => 'مطبخ مجهز,موقف سيارة,مصعد,أمن 24 ساعة',
            'features_en' => 'Equipped Kitchen,Parking,Elevator,24h Security',
            'featured' => 1,
            'status' => 'active'
        ]
        // Add more properties here...
    ];

    $sql = "INSERT INTO properties (title_ar, title_en, description_ar, description_en, type, type_ar, price, location_ar, location_en, area, bedrooms, bathrooms, features_ar, features_en, featured, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $pdo->prepare($sql);

    foreach ($properties as $property) {
        $stmt->execute([
            $property['title_ar'],
            $property['title_en'],
            $property['description_ar'],
            $property['description_en'],
            $property['type'],
            $property['type_ar'],
            $property['price'],
            $property['location_ar'],
            $property['location_en'],
            $property['area'],
            $property['bedrooms'],
            $property['bathrooms'],
            $property['features_ar'],
            $property['features_en'],
            $property['featured'],
            $property['status']
        ]);
        echo "Property added: " . $property['title_en'] . "\n";
    }

    echo "All properties added successfully!";

} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
```

---

## **🎛️ Property Types**

### **Available Types:**
- **`residential`** (سكني) - Apartments, Villas, Houses
- **`commercial`** (تجاري) - Shops, Offices, Warehouses
- **`administrative`** (إداري) - Office Buildings, Complexes

### **Status Options:**
- **`active`** - Visible on website
- **`sold`** - Marked as sold
- **`rented`** - Marked as rented
- **`inactive`** - Hidden from website

---

## **⭐ Featured Properties**

### **To Make Property Featured:**
```sql
UPDATE properties SET featured = 1 WHERE id = [PROPERTY_ID];
```

### **Featured Properties Display:**
- Show on homepage
- Appear first in listings
- Special highlighting

---

## **📊 Property Management**

### **View All Properties:**
```sql
SELECT id, title_en, type, price, location_en, status, featured
FROM properties
ORDER BY created_at DESC;
```

### **Update Property Status:**
```sql
UPDATE properties SET status = 'sold' WHERE id = [PROPERTY_ID];
```

### **Delete Property:**
```sql
DELETE FROM properties WHERE id = [PROPERTY_ID];
```

---

## **🔍 Testing Properties**

### **Check Property Display:**
1. **Arabic Version**: Visit `/العروض-العقارية`
2. **English Version**: Visit `/en/advertisements`
3. **Individual Property**: Visit `/en/advertisements/view/[ID]`

### **Verify Features:**
- ✅ Bilingual display
- ✅ Image loading
- ✅ Price formatting
- ✅ Feature lists
- ✅ Contact forms

---

## **📱 Property Features**

### **Automatic Features:**
- ✅ **Responsive Design** - Works on all devices
- ✅ **SEO Friendly** - Search engine optimized
- ✅ **Bilingual Support** - Arabic/English
- ✅ **Image Gallery** - Multiple images per property
- ✅ **Contact Integration** - Inquiry forms
- ✅ **View Tracking** - Visitor analytics
- ✅ **Search & Filter** - Advanced filtering
- ✅ **Related Properties** - Suggestions

---

## **🎛️ Method 4: Web Admin Interface**

### **Access Admin Panel:**
Visit: `http://localhost/alyanabea-website/admin`

### **Admin Features:**
- ✅ **Dashboard** - View all properties
- ✅ **Add Properties** - Web form interface
- ✅ **Edit Properties** - Modify existing properties
- ✅ **Delete Properties** - Remove properties
- ✅ **Toggle Featured** - Mark/unmark as featured
- ✅ **Statistics** - Property counts and views
- ✅ **Bilingual Support** - Arabic/English forms

### **Admin URLs:**
- **Dashboard**: `/admin`
- **Add Property**: `/admin/add_property`
- **Edit Property**: `/admin/edit_property/[ID]`
- **Delete Property**: `/admin/delete_property/[ID]`
- **Toggle Featured**: `/admin/toggle_featured/[ID]`

---

## **🚀 Quick Start Guide**

### **1. Access Admin Panel:**
```
http://localhost/alyanabea-website/admin
```

### **2. Add Your First Property:**
- Click "Add Property" button
- Fill in Arabic and English details
- Set price, location, and features
- Mark as featured if desired
- Submit form

### **3. Manage Properties:**
- View all properties in dashboard
- Edit, delete, or feature properties
- Monitor views and statistics

### **4. View on Website:**
- Visit `/العروض-العقارية` (Arabic)
- Visit `/en/advertisements` (English)
- Properties appear automatically

The property system is fully functional and ready to use! Add properties using any of the methods above and they will automatically appear on the website with full bilingual support.
