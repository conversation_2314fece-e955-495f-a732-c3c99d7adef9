# Contact Form Not Working - Troubleshooting Guide 🔧

## 🎯 **Step-by-Step Debugging Process**

Since the contact form is still not working, let's debug this systematically:

---

## **Step 1: Run the Debug Script**

1. **Upload** `debug_contact_form.php` to your website root
2. **Visit**: `http://localhost/alyanabea-website/debug_contact_form.php`
3. **Fill out the test form** and submit it
4. **Check results** - this will tell us exactly what's failing

---

## **Step 2: Check Database Table**

### **Verify Contacts Table Exists:**
1. **Open phpMyAdmin**
2. **Select database**: `alyanabe_webart`
3. **Look for `contacts` table**

### **If Table Doesn't Exist:**
```sql
CREATE TABLE contacts (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(100) NOT NULL,
  email varchar(100) NOT NULL,
  phone varchar(20) NOT NULL,
  subject varchar(200) NOT NULL,
  message text NOT NULL,
  language varchar(2) DEFAULT 'ar',
  status enum('new','read','replied','archived') DEFAULT 'new',
  ip_address varchar(45) DEFAULT NULL,
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

---

## **Step 3: Check CodeIgniter Logs**

### **Enable Logging:**
1. **Edit**: `application/config/config.php`
2. **Find**: `$config['log_threshold']`
3. **Set to**: `$config['log_threshold'] = 4;` (for all logs)

### **Check Log Files:**
1. **Location**: `application/logs/`
2. **Look for**: Files named like `log-2024-01-15.php`
3. **Check for**: Contact form errors

---

## **Step 4: Enable Error Display**

### **Temporarily Enable Errors:**
1. **Edit**: `index.php` (main file)
2. **Add at top** (after `<?php`):
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### **Test Contact Form:**
1. **Visit**: `/اتصل-بنا`
2. **Submit form**
3. **Look for**: Any PHP errors displayed

---

## **Step 5: Check Form Submission**

### **Common Issues:**

**1. Form Not Submitting:**
- Check if JavaScript is blocking submission
- Verify form action URL is correct
- Check for CSRF token issues

**2. Validation Failing:**
- Check required fields are filled
- Verify email format is correct
- Check field name matches validation rules

**3. Database Connection:**
- Verify database credentials in `config/database.php`
- Check if database server is running
- Test connection with debug script

**4. Table Structure:**
- Verify all required columns exist
- Check column names match model
- Verify data types are correct

---

## **Step 6: Manual Testing**

### **Test Database Directly:**
```sql
-- Test inserting a record manually
INSERT INTO contacts (name, email, phone, subject, message, language, created_at) 
VALUES ('Test User', '<EMAIL>', '+966501234567', 'Test Subject', 'Test message', 'ar', NOW());

-- Check if record was inserted
SELECT * FROM contacts ORDER BY created_at DESC LIMIT 1;
```

### **Test CodeIgniter Database:**
Create a simple test file:
```php
<?php
// test_db.php
require_once 'system/core/CodeIgniter.php';

$CI =& get_instance();
$CI->load->database();

$data = array(
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'phone' => '+966501234567',
    'subject' => 'Test Subject',
    'message' => 'Test message',
    'language' => 'ar'
);

if ($CI->db->insert('contacts', $data)) {
    echo "Success! Insert ID: " . $CI->db->insert_id();
} else {
    echo "Failed: " . $CI->db->last_query();
    print_r($CI->db->error());
}
?>
```

---

## **Step 7: Check Form HTML**

### **Verify Form Structure:**
1. **Check**: Form has `method="post"`
2. **Check**: Form action points to correct URL
3. **Check**: All input fields have `name` attributes
4. **Check**: Required fields are marked properly

### **Example Correct Form:**
```html
<form method="post" action="">
    <input type="text" name="name" required>
    <input type="email" name="email" required>
    <input type="tel" name="phone" required>
    <input type="text" name="subject" required>
    <textarea name="message" required></textarea>
    <input type="submit" value="Send">
</form>
```

---

## **Step 8: Common Solutions**

### **Solution 1: Missing Table**
- **Problem**: `contacts` table doesn't exist
- **Fix**: Run the CREATE TABLE SQL above

### **Solution 2: Wrong Database Config**
- **Problem**: Database connection fails
- **Fix**: Check `application/config/database.php` settings

### **Solution 3: Validation Issues**
- **Problem**: Form validation failing
- **Fix**: Check validation rules in Contact controller

### **Solution 4: CSRF Token**
- **Problem**: CSRF protection blocking form
- **Fix**: Add CSRF token to form or disable CSRF for contact form

### **Solution 5: JavaScript Errors**
- **Problem**: JavaScript preventing form submission
- **Fix**: Check browser console for errors

---

## **Step 9: Quick Diagnostic Checklist**

Run through this checklist:

- [ ] **Database connection works** (test with debug script)
- [ ] **Contacts table exists** (check in phpMyAdmin)
- [ ] **Form submits data** (check with debug script)
- [ ] **No PHP errors** (enable error display)
- [ ] **No JavaScript errors** (check browser console)
- [ ] **Validation passes** (check log files)
- [ ] **CodeIgniter logs show activity** (check application/logs/)

---

## **Step 10: Get Specific Error**

### **Run Debug Script and Report:**
1. **Visit**: `debug_contact_form.php`
2. **Submit test form**
3. **Copy the exact error message**
4. **Check what step fails**

### **Common Error Messages:**
- **"Table doesn't exist"** → Create contacts table
- **"Connection failed"** → Fix database config
- **"Validation failed"** → Check form fields
- **"Insert failed"** → Check table structure

---

## **🔧 Next Steps**

1. **Run the debug script first** - this will tell us exactly what's wrong
2. **Create the contacts table** if it doesn't exist
3. **Enable error logging** to see detailed errors
4. **Test step by step** using the checklist above

**The debug script will show us exactly where the problem is!**

---

## **📞 Quick Help**

**Most Common Issues:**
1. **Missing contacts table** (90% of cases)
2. **Wrong database config** (5% of cases)
3. **Form validation issues** (3% of cases)
4. **JavaScript conflicts** (2% of cases)

**Run `debug_contact_form.php` and let me know what it shows!** 🔍
