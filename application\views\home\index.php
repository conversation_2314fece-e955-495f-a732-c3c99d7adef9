<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="row align-items-center min-vh-75">
                <div class="col-lg-8 col-xl-7">
                    <div class="hero-content text-white">
                        <h1 class="hero-title display-4 fw-bold mb-4">
                            <?php echo $this->lang->line('home_title'); ?>
                        </h1>
                        <p class="hero-subtitle lead mb-4">
                            <?php echo $this->lang->line('home_subtitle'); ?>
                        </p>
                        <div class="hero-buttons">
                            <a href="<?php echo $is_english ? base_url('en/advertisements') : base_url('العروض-العقارية'); ?>"
                               class="btn btn-light btn-lg me-3 mb-2">
                                <i class="fas fa-building"></i> <?php echo $this->lang->line('nav_advertisements'); ?>
                            </a>
                            <a href="<?php echo $is_english ? base_url('en/contact') : base_url('اتصل-بنا'); ?>"
                               class="btn btn-outline-light btn-lg mb-2">
                                <i class="fas fa-phone"></i> <?php echo $this->lang->line('contact_us'); ?>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-xl-5 d-none d-lg-block">
                    <div class="hero-image">
                        <?php if (file_exists(FCPATH . 'assets/images/hero-building.jpg')): ?>
                            <img src="<?php echo base_url('assets/images/hero-building.jpg'); ?>"
                                 alt="<?php echo $this->lang->line('company_name'); ?>"
                                 class="img-fluid rounded shadow-lg">
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="about-images">
                    <div class="row g-3">
                        <div class="col-6">
                            <?php if (file_exists(FCPATH . 'assets/images/about1.jpg')): ?>
                                <img src="<?php echo base_url('assets/images/about1.jpg'); ?>"
                                     alt="About 1" class="img-fluid rounded shadow">
                            <?php else: ?>
                                <div class="about-placeholder rounded shadow"
                                     style="height: 150px; background: url('https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80') center/cover; border-radius: 8px;">
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-6">
                            <?php if (file_exists(FCPATH . 'assets/images/about2.jpg')): ?>
                                <img src="<?php echo base_url('assets/images/about2.jpg'); ?>"
                                     alt="About 2" class="img-fluid rounded shadow">
                            <?php else: ?>
                                <div class="about-placeholder rounded shadow"
                                     style="height: 150px; background: url('https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80') center/cover; border-radius: 8px;">
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-12 mt-3">
                            <?php if (file_exists(FCPATH . 'assets/images/about3.jpg')): ?>
                                <img src="<?php echo base_url('assets/images/about3.jpg'); ?>"
                                     alt="About 3" class="img-fluid rounded shadow">
                            <?php else: ?>
                                <div class="about-placeholder rounded shadow"
                                     style="height: 200px; min-height: 200px; background: url('https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg?auto=compress&cs=tinysrgb&w=800') center/cover; border-radius: 8px; background-size: cover; display: block !important; width: 100% !important; margin-top: 10px;">
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="about-content">
                    <h2 class="section-title mb-4">
                        <?php echo $this->lang->line('about_alyanabea'); ?>
                    </h2>
                    <p class="text-muted mb-4">
                        <?php echo $this->lang->line('about_description'); ?>
                    </p>
                    <a href="<?php echo $is_english ? base_url('en/about') : base_url('من-نحن'); ?>"
                       class="btn btn-primary">
                        <?php echo $this->lang->line('read_more'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title"><?php echo $this->lang->line('our_services'); ?></h2>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="service-card text-center h-100">
                    <div class="service-icon mb-3">
                        <i class="fas fa-building fa-3x text-primary"></i>
                    </div>
                    <h5 class="service-title"><?php echo $this->lang->line('property_management'); ?></h5>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="service-card text-center h-100">
                    <div class="service-icon mb-3">
                        <i class="fas fa-handshake fa-3x text-primary"></i>
                    </div>
                    <h5 class="service-title"><?php echo $this->lang->line('buy_sell'); ?></h5>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="service-card text-center h-100">
                    <div class="service-icon mb-3">
                        <i class="fas fa-chart-line fa-3x text-primary"></i>
                    </div>
                    <h5 class="service-title"><?php echo $this->lang->line('property_marketing'); ?></h5>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="service-card text-center h-100">
                    <div class="service-icon mb-3">
                        <i class="fas fa-tools fa-3x text-primary"></i>
                    </div>
                    <h5 class="service-title"><?php echo $this->lang->line('maintenance'); ?></h5>
                </div>
            </div>
        </div>
    </div>
</section>




<!-- Statistics Section -->
<section class="stats-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title text-white"><?php echo $this->lang->line('what_makes_us_different'); ?></h2>
            </div>
        </div>
        <div class="row text-center">
            <div class="col-lg-4 col-md-4 mb-4">
                <div class="stat-item">
                    <div class="stat-number display-4 fw-bold mb-2">
                        <?php echo $stats['years_experience']; ?>+
                    </div>
                    <div class="stat-label h5">
                        <?php echo $this->lang->line('years_experience'); ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-4 mb-4">
                <div class="stat-item">
                    <div class="stat-number display-4 fw-bold mb-2">
                        <?php echo "500+" ?>+
                    </div>
                    <div class="stat-label h5">
                        <?php echo $this->lang->line('properties'); ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-4 mb-4">
                <div class="stat-item">
                    <div class="stat-number display-4 fw-bold mb-2">
                        <?php echo $stats['total_clients']; ?>+
                    </div>
                    <div class="stat-label h5">
                        <?php echo $this->lang->line('clients'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
