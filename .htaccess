RewriteEngine on
RewriteCond $1 !^(index\.php|assets|images|image|resources|robots\.txt)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?/$1 [L,QSA]

# Ensure proper MIME types for images
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
</IfModule>

# Allow access to assets directory
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_URI} ^/assets/.*$
    RewriteRule ^.*$ - [L]
</IfModule>

